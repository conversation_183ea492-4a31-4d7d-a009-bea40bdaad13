import { parseTime } from "@/utils/ruoyi";

/**
 * 菜单管理 - 表单配置
 */
export function createMenuFormOption(proxy) {
  return {
    // 弹窗配置
    dialogWidth: '800px',
    dialogHeight: '70vh',
    labelWidth: '120px',
    
    // 表单字段配置
    column: [
      // ==================== 基础信息分组 ====================
      {
        label: "基础信息",
        prop: "divider_basic_info",
        formSlot: true,
        headerAlign: "left",
        labelWidth: 0,
        span: 12,
        divider: true
      },
      {
        label: "上级菜单",
        prop: "parentId",
        type: 'tree-select',
        span: 24,
        placeholder: "选择上级菜单",
        treeProps: {
          value: 'menuId',
          label: 'menuName',
          children: 'children'
        },
        checkStrictly: true,
        formSlot: true // 使用自定义插槽
      },
      {
        label: "菜单类型",
        prop: "menuType",
        type: 'radio',
        span: 24,
        required: true,
        defaultValue: 'M',
        dicData: [
          { label: '目录', value: 'M' },
          { label: '菜单', value: 'C' },
          { label: '按钮', value: 'F' }
        ],
        control: (val, form) => {
          // 根据菜单类型控制字段显示
          const isNotButton = val !== 'F'
          const isMenu = val === 'C'
          const isNotDirectory = val !== 'M'
          
          return {
            icon: {
              viewDisplay: isNotButton,
              addDisplay: isNotButton,
              editDisplay: isNotButton
            },
            routeName: {
              viewDisplay: isMenu,
              addDisplay: isMenu,
              editDisplay: isMenu
            },
            isFrame: {
              viewDisplay: isNotButton,
              addDisplay: isNotButton,
              editDisplay: isNotButton
            },
            path: {
              viewDisplay: isNotButton,
              addDisplay: isNotButton,
              editDisplay: isNotButton
            },
            component: {
              viewDisplay: isMenu,
              addDisplay: isMenu,
              editDisplay: isMenu
            },
            perms: {
              viewDisplay: isNotDirectory,
              addDisplay: isNotDirectory,
              editDisplay: isNotDirectory
            },
            query: {
              viewDisplay: isMenu,
              addDisplay: isMenu,
              editDisplay: isMenu
            },
            isCache: {
              viewDisplay: isMenu,
              addDisplay: isMenu,
              editDisplay: isMenu
            },
            visible: {
              viewDisplay: isNotButton,
              addDisplay: isNotButton,
              editDisplay: isNotButton
            }
          }
        }
      },
      {
        label: "菜单名称",
        prop: "menuName",
        type: 'input',
        span: 12,
        required: true,
        placeholder: "请输入菜单名称",
        rules: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" }
        ]
      },
      {
        label: "显示排序",
        prop: "orderNum",
        type: 'number',
        span: 12,
        required: true,
        controlsPosition: 'right',
        min: 0,
        defaultValue: 0,
        rules: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" }
        ]
      },
      {
        label: "菜单图标",
        prop: "icon",
        type: 'input',
        span: 12,
        placeholder: "点击选择图标",
        readonly: true,
        formSlot: true // 使用自定义图标选择插槽
      },
      {
        label: "路由名称",
        prop: "routeName",
        type: 'input',
        span: 12,
        placeholder: "请输入路由名称",
        tooltip: "默认不填则和路由地址相同：如地址为：`user`，则名称为`User`（注意：因为router会删除名称相同路由，为避免名字的冲突，特殊情况下请自定义，保证唯一性）"
      },
      
      // ==================== 路由配置分组 ====================
      {
        label: "路由配置",
        prop: "divider_route_config",
        formSlot: true,
        headerAlign: "left",
        labelWidth: 0,
        span: 24,
        divider: true
      },
      {
        label: "是否外链",
        prop: "isFrame",
        type: 'radio',
        span: 12,
        defaultValue: '1',
        tooltip: "选择是外链则路由地址需要以`http(s)://`开头",
        dicData: [
          { label: '是', value: '0' },
          { label: '否', value: '1' }
        ]
      },
      {
        label: "路由地址",
        prop: "path",
        type: 'input',
        span: 12,
        required: true,
        placeholder: "请输入路由地址",
        tooltip: "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
        rules: [
          { required: true, message: "路由地址不能为空", trigger: "blur" }
        ]
      },
      {
        label: "组件路径",
        prop: "component",
        type: 'input',
        span: 12,
        placeholder: "请输入组件路径",
        tooltip: "访问的组件路径，如：`system/user/index`，默认在`views`目录下"
      },
      {
        label: "路由参数",
        prop: "query",
        type: 'input',
        span: 12,
        placeholder: "请输入路由参数",
        maxlength: 255,
        tooltip: '访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`'
      },
      
      // ==================== 权限配置分组 ====================
      {
        label: "权限配置",
        prop: "divider_permission_config",
        formSlot: true,
        headerAlign: "left",
        labelWidth: 0,
        span: 24,
        divider: true
      },
      {
        label: "权限字符",
        prop: "perms",
        type: 'input',
        span: 12,
        placeholder: "请输入权限标识",
        maxlength: 100,
        tooltip: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
      },
      {
        label: "是否缓存",
        prop: "isCache",
        type: 'radio',
        span: 12,
        defaultValue: '0',
        tooltip: "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",
        dicData: [
          { label: '缓存', value: '0' },
          { label: '不缓存', value: '1' }
        ]
      },
      {
        label: "显示状态",
        prop: "visible",
        type: 'radio',
        span: 12,
        defaultValue: '0',
        tooltip: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
        dicData: [
          { label: '显示', value: '0' },
          { label: '隐藏', value: '1' }
        ]
      },
      {
        label: "菜单状态",
        prop: "status",
        type: 'radio',
        span: 12,
        defaultValue: '0',
        tooltip: "选择停用则路由将不会出现在侧边栏，也不能被访问",
        dicData: [
          { label: '正常', value: '0' },
          { label: '停用', value: '1' }
        ]
      }
    ]
  }
} 