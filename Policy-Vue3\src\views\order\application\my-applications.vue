<template>
  <div class="my-applications-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>我的培训申请</h1>
      <p>查看您的所有培训申请记录和状态</p>
    </div>

    <!-- 申请列表 -->
    <div class="applications-list" v-loading="loading">
      <div v-if="applicationList.length === 0" class="empty-state">
        <el-empty description="暂无申请记录">
          <el-button type="primary" @click="goToApply">去申请培训</el-button>
        </el-empty>
      </div>

      <div v-else class="application-items">
        <div v-for="application in applicationList" :key="application.applicationId" class="application-item" :class="{
          'status-pending': application.applicationStatus === '0',
          'status-approved': application.applicationStatus === '1',
          'status-rejected': application.applicationStatus === '2',
          'status-cancelled': application.applicationStatus === '3'
        }">
          <div class="application-content">
            <div class="application-header">
              <h3 class="training-title">{{ application.orderTitle }}</h3>
              <el-tag :type="getStatusTagType(application.applicationStatus)" size="default">
                {{ getStatusText(application.applicationStatus) }}
              </el-tag>
            </div>

            <div class="application-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">申请人：</span>
                  <span class="value">{{ application.applicantName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ application.applicantPhone }}</span>
                </div>
                <div class="info-item">
                  <span class="label">申请时间：</span>
                  <span class="value">{{ formatDateTime(application.applicationTime) }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <span class="label">培训类型：</span>
                  <span class="value">{{ application.trainingType }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训时间：</span>
                  <span class="value">{{ formatDate(application.startDate) }} 至 {{ formatDate(application.endDate) }}</span>
                </div>
              </div>

              <div v-if="application.reviewTime" class="info-row">
                <div class="info-item">
                  <span class="label">审核时间：</span>
                  <span class="value">{{ formatDateTime(application.reviewTime) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">审核人：</span>
                  <span class="value">{{ application.reviewer || '--' }}</span>
                </div>
              </div>

              <div v-if="application.reviewComment" class="info-row">
                <div class="info-item full-width">
                  <span class="label">审核意见：</span>
                  <span class="value">{{ application.reviewComment }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="application-actions">
            <el-button type="primary" size="default" @click="handleViewDetail(application)">
              <el-icon>
                <View />
              </el-icon>
              查看详情
            </el-button>

            <el-button v-if="application.applicationStatus === '0'" type="warning" size="default"
              @click="handleCancel(application)">
              <el-icon>
                <Close />
              </el-icon>
              取消申请
            </el-button>

            <el-button v-if="application.applicationStatus === '2'" type="success" size="default"
              @click="handleReapply(application)">
              <el-icon>
                <Refresh />
              </el-icon>
              重新申请
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="申请详情" width="700px" append-to-body>
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions title="培训信息" :column="2" border>
          <el-descriptions-item label="培训标题">{{ currentApplication.orderTitle }}</el-descriptions-item>
          <el-descriptions-item label="培训类型">{{ currentApplication.trainingType }}</el-descriptions-item>
          <el-descriptions-item label="培训时间">
            {{ formatDate(currentApplication.startDate) }} 至 {{ formatDate(currentApplication.endDate) }}
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="申请信息" :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="申请人姓名">{{ currentApplication.applicantName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentApplication.applicantPhone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱地址">{{ currentApplication.applicantEmail }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentApplication.applicantGender }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentApplication.applicantAge }}</el-descriptions-item>
          <el-descriptions-item label="学历">{{ currentApplication.applicantEducation }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ currentApplication.applicantIdCard }}</el-descriptions-item>
          <el-descriptions-item label="联系地址">{{ currentApplication.applicantAddress }}</el-descriptions-item>
          <el-descriptions-item label="工作经验" :span="2">{{ currentApplication.applicantExperience || '--' }}</el-descriptions-item>
          <el-descriptions-item label="申请备注" :span="2">{{ currentApplication.applicationNote || '--' }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions title="审核信息" :column="2" border style="margin-top: 20px;">
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusTagType(currentApplication.applicationStatus)">
              {{ getStatusText(currentApplication.applicationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentApplication.applicationTime) }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ formatDateTime(currentApplication.reviewTime) }}</el-descriptions-item>
          <el-descriptions-item label="审核人">{{ currentApplication.reviewer || '--' }}</el-descriptions-item>
          <el-descriptions-item v-if="currentApplication.reviewComment" label="审核意见" :span="2">
            {{ currentApplication.reviewComment }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, Close, Refresh } from '@element-plus/icons-vue'
import { getMyApplications, cancelMyApplication } from '@/api/training/application'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const applicationList = ref([])
const detailDialogVisible = ref(false)
const currentApplication = ref(null)

// 生命周期
onMounted(() => {
  loadMyApplications()
})

// 方法
const loadMyApplications = async () => {
  loading.value = true
  try {
    const response = await getMyApplications()
    applicationList.value = response.data || []
  } catch (error) {
    ElMessage.error('获取申请记录失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleViewDetail = (application) => {
  currentApplication.value = application
  detailDialogVisible.value = true
}

const handleCancel = async (application) => {
  try {
    await ElMessageBox.confirm('确认要取消申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyApplication(application.applicationId)
    ElMessage.success('取消申请成功')

    // 重新加载申请列表
    await loadMyApplications()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消申请失败')
    }
  }
}

const handleReapply = (application) => {
  // 跳转到申请页面，并传递培训订单ID
  router.push({
    path: '/zhaop/application/signup',
    query: { orderId: application.orderId }
  })
}

const goToApply = () => {
  router.push('/zhaop/application/signup')
}

const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatDate = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.my-applications-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: #303133;
    margin-bottom: 10px;
    font-size: 28px;
  }

  p {
    color: #606266;
    font-size: 16px;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.application-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
}

.application-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.status-pending {
    border-left: 4px solid #e6a23c;
  }

  &.status-approved {
    border-left: 4px solid #67c23a;
  }

  &.status-rejected {
    border-left: 4px solid #f56c6c;
  }

  &.status-cancelled {
    border-left: 4px solid #909399;
  }
}

.application-content {
  flex: 1;
}

.application-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .training-title {
    color: #303133;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }
}

.application-info {
  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;

    .info-item {
      flex: 1;
      min-width: 150px;

      &.full-width {
        flex: 100%;
      }

      .label {
        font-weight: 500;
        color: #606266;
        margin-right: 5px;
      }

      .value {
        color: #303133;
      }
    }
  }
}

.application-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  .el-button {
    flex: 1;
    min-width: 100px;
  }
}

.application-detail {
  .el-descriptions {
    margin-bottom: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .application-items {
    grid-template-columns: 1fr;
  }

  .application-item {
    padding: 15px;
  }

  .application-actions {
    .el-button {
      min-width: 80px;
      font-size: 12px;
    }
  }

  .info-row {
    .info-item {
      min-width: 120px;
      font-size: 14px;
    }
  }
}
</style>
