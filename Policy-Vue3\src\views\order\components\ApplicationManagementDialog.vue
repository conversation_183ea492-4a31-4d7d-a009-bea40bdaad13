<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1400px"
    append-to-body
  >
    <div class="application-management-container" v-if="orderData">
      <!-- 订单信息 -->
      <div class="order-summary">
        <h3>{{ orderData.orderTitle }}</h3>
        <div class="summary-info">
          <span class="info-item">培训类型：{{ orderData.trainingType }}</span>
          <span class="info-item">培训级别：{{ orderData.trainingLevel }}</span>
          <span class="info-item">招生人数：{{ orderData.maxParticipants }}人</span>
          <span class="info-item">
            申请数量：<el-tag type="primary">{{ applications.length }}</el-tag>
          </span>
        </div>
      </div>

      <!-- 申请列表 -->
      <div class="applications-section">
        <div class="section-header">
          <h4>培训机构申请列表</h4>
          <div class="filter-controls">
            <el-select v-model="statusFilter" placeholder="筛选状态" style="width: 150px;" @change="filterApplications">
              <el-option label="全部" value=""></el-option>
              <el-option label="待审核" value="0"></el-option>
              <el-option label="审核通过" value="1"></el-option>
              <el-option label="审核拒绝" value="2"></el-option>
            </el-select>
          </div>
        </div>

        <div class="applications-grid">
          <div v-for="application in filteredApplications" :key="application.applicationId" class="application-card">
            <div class="card-header">
              <div class="institution-info">
                <h5>{{ application.institutionName }}</h5>
                <span class="contact-info">{{ application.contactPerson }} | {{ application.contactPhone }}</span>
              </div>
              <div class="status-info">
                <el-tag :type="getApplicationStatusType(application.applicationStatus)">
                  {{ getApplicationStatusText(application.applicationStatus) }}
                </el-tag>
                <span class="apply-time">{{ formatDateTime(application.applicationTime) }}</span>
              </div>
            </div>

            <div class="card-content">
              <div class="basic-info">
                <div class="info-row">
                  <span class="label">培训周期：</span>
                  <span class="value">{{ application.trainingDuration }}</span>
                </div>
                <div class="info-row">
                  <span class="label">培训地点：</span>
                  <span class="value">{{ application.trainingLocation }}</span>
                </div>
                <div class="info-row">
                  <span class="label">师资数量：</span>
                  <span class="value">{{ application.teacherCount }}人</span>
                </div>
                <div class="info-row">
                  <span class="label">最大容量：</span>
                  <span class="value">{{ application.maxTrainingCapacity }}人</span>
                </div>
              </div>

              <div class="training-plan">
                <h6>培训方案</h6>
                <p class="plan-text">{{ application.trainingPlan }}</p>
              </div>

              <div class="teacher-info">
                <h6>师资介绍</h6>
                <p class="teacher-text">{{ application.teacherInfo }}</p>
              </div>

              <div class="qualifications" v-if="application.qualifications">
                <h6>机构资质</h6>
                <p class="qualification-text">{{ application.qualifications }}</p>
              </div>

              <!-- 文件附件 -->
              <div class="attachments" v-if="hasAttachments(application)">
                <h6>相关材料</h6>
                <div class="file-list">
                  <div v-if="application.businessLicense && application.businessLicense.length" class="file-group">
                    <span class="file-label">营业执照：</span>
                    <div class="file-items">
                      <el-link v-for="file in application.businessLicense" :key="file.uid" type="primary" @click="previewFile(file)">
                        {{ file.name }}
                      </el-link>
                    </div>
                  </div>
                  <div v-if="application.qualificationCertificates && application.qualificationCertificates.length" class="file-group">
                    <span class="file-label">资质证书：</span>
                    <div class="file-items">
                      <el-link v-for="file in application.qualificationCertificates" :key="file.uid" type="primary" @click="previewFile(file)">
                        {{ file.name }}
                      </el-link>
                    </div>
                  </div>
                  <div v-if="application.teacherCertificates && application.teacherCertificates.length" class="file-group">
                    <span class="file-label">师资证明：</span>
                    <div class="file-items">
                      <el-link v-for="file in application.teacherCertificates" :key="file.uid" type="primary" @click="previewFile(file)">
                        {{ file.name }}
                      </el-link>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card-footer" v-if="application.applicationStatus === '0'">
              <el-button size="small" @click="viewFullApplication(application)">查看详情</el-button>
              <el-button size="small" type="success" @click="approveApplication(application)">
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button size="small" type="danger" @click="rejectApplication(application)">
                <el-icon><Close /></el-icon>
                拒绝
              </el-button>
            </div>
            <div class="card-footer" v-else>
              <el-button size="small" @click="viewFullApplication(application)">查看详情</el-button>
              <span class="review-info" v-if="application.reviewTime">
                审核时间：{{ formatDateTime(application.reviewTime) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredApplications.length === 0" class="empty-state">
          <el-empty description="暂无申请记录" />
        </div>
      </div>
    </div>

    <!-- 审核拒绝弹窗 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="拒绝申请"
      width="500px"
      append-to-body
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmReject">确认拒绝</el-button>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ApplicationManagementDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue'
import { Check, Close } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['approve', 'reject'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('申请管理')
const orderData = ref(null)
const applications = ref([])
const statusFilter = ref('')
const rejectDialogVisible = ref(false)
const currentApplication = ref(null)

// 拒绝表单
const rejectForm = reactive({
  reason: ''
})

// 过滤后的申请列表
const filteredApplications = computed(() => {
  if (!statusFilter.value) {
    return applications.value
  }
  return applications.value.filter(app => app.applicationStatus === statusFilter.value)
})

// 打开弹窗
const openDialog = async (order) => {
  orderData.value = order
  dialogVisible.value = true
  
  // 加载申请列表
  await loadApplications(order.orderId)
}

// 加载申请列表
const loadApplications = async (orderId) => {
  try {
    // 这里应该调用API获取申请列表
    // const response = await getTrainingApplicationsByOrderId(orderId)
    // applications.value = response.data || []
    
    // 临时模拟数据
    applications.value = [
      {
        applicationId: 1,
        institutionName: '青岛职业技能培训中心',
        contactPerson: '张老师',
        contactPhone: '13800138001',
        contactEmail: '<EMAIL>',
        trainingDuration: '80课时',
        trainingLocation: '青岛市市南区香港中路10号',
        teacherCount: 5,
        maxTrainingCapacity: 50,
        trainingPlan: '本培训方案采用理论与实践相结合的方式，通过系统的课程设计和实际项目操作，帮助学员掌握核心技能...',
        teacherInfo: '拥有10年以上行业经验的资深讲师团队，包括高级工程师、项目经理等专业人士...',
        qualifications: '国家认定的职业技能培训机构，具有相关培训资质和认证...',
        applicationTime: '2025-07-23 10:30:00',
        applicationStatus: '0',
        businessLicense: [{ uid: 1, name: '营业执照.pdf', url: '/files/license.pdf' }],
        qualificationCertificates: [{ uid: 2, name: '培训资质证书.pdf', url: '/files/cert.pdf' }],
        teacherCertificates: [{ uid: 3, name: '师资证明.pdf', url: '/files/teacher.pdf' }]
      }
    ]
  } catch (error) {
    console.error('加载申请列表失败:', error)
    proxy.$modal.msgError('加载申请列表失败')
  }
}

// 筛选申请
const filterApplications = () => {
  // 筛选逻辑已通过computed实现
}

// 查看完整申请
const viewFullApplication = (application) => {
  // 打开申请详情弹窗或跳转到详情页面
  console.log('查看完整申请:', application)
}

// 通过申请
const approveApplication = async (application) => {
  try {
    await proxy.$modal.confirm(`确认通过"${application.institutionName}"的申请吗？`)
    emit('approve', application.applicationId)
    
    // 更新本地状态
    application.applicationStatus = '1'
    application.reviewTime = new Date().toISOString()
    
    proxy.$modal.msgSuccess('申请已通过')
  } catch (error) {
    // 用户取消操作
  }
}

// 拒绝申请
const rejectApplication = (application) => {
  currentApplication.value = application
  rejectForm.reason = ''
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.reason.trim()) {
    proxy.$modal.msgError('请输入拒绝原因')
    return
  }
  
  try {
    emit('reject', currentApplication.value.applicationId, rejectForm.reason)
    
    // 更新本地状态
    currentApplication.value.applicationStatus = '2'
    currentApplication.value.reviewTime = new Date().toISOString()
    currentApplication.value.rejectReason = rejectForm.reason
    
    rejectDialogVisible.value = false
    proxy.$modal.msgSuccess('申请已拒绝')
  } catch (error) {
    proxy.$modal.msgError('操作失败')
  }
}

// 预览文件
const previewFile = (file) => {
  // 打开文件预览
  window.open(file.url, '_blank')
}

// 检查是否有附件
const hasAttachments = (application) => {
  return (application.businessLicense && application.businessLicense.length) ||
         (application.qualificationCertificates && application.qualificationCertificates.length) ||
         (application.teacherCertificates && application.teacherCertificates.length) ||
         (application.trainingCases && application.trainingCases.length)
}

// 状态处理方法
const getApplicationStatusType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待审核
    '1': 'success',  // 审核通过
    '2': 'danger',   // 审核拒绝
    '3': 'info'      // 已取消
  }
  return statusMap[status] || 'info'
}

const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '审核通过',
    '2': '审核拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

// 日期格式化
const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style lang="scss" scoped>
.application-management-container {
  max-height: 75vh;
  overflow-y: auto;
}

.order-summary {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  h3 {
    color: #303133;
    font-size: 18px;
    margin: 0 0 10px 0;
    font-weight: 600;
  }

  .summary-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    .info-item {
      color: #606266;
      font-size: 14px;
    }
  }
}

.applications-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e4e7ed;

    h4 {
      color: #303133;
      font-size: 16px;
      margin: 0;
      font-weight: 600;
    }
  }
}

.applications-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.application-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #409eff;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f2f5;

  .institution-info {
    h5 {
      color: #303133;
      font-size: 16px;
      margin: 0 0 5px 0;
      font-weight: 600;
    }

    .contact-info {
      color: #909399;
      font-size: 13px;
    }
  }

  .status-info {
    text-align: right;

    .apply-time {
      display: block;
      color: #909399;
      font-size: 12px;
      margin-top: 5px;
    }
  }
}

.card-content {
  .basic-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;

    .info-row {
      display: flex;
      font-size: 14px;

      .label {
        color: #909399;
        width: 80px;
        flex-shrink: 0;
      }

      .value {
        color: #303133;
        flex: 1;
      }
    }
  }

  h6 {
    color: #409eff;
    font-size: 14px;
    margin: 15px 0 8px 0;
    font-weight: 600;
  }

  .plan-text,
  .teacher-text,
  .qualification-text {
    color: #606266;
    font-size: 13px;
    line-height: 1.5;
    margin: 0 0 15px 0;
    padding: 10px;
    background: #fafbfc;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .attachments {
    .file-list {
      .file-group {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        font-size: 13px;

        .file-label {
          color: #909399;
          width: 80px;
          flex-shrink: 0;
        }

        .file-items {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          flex: 1;
        }
      }
    }
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;

  .review-info {
    color: #909399;
    font-size: 12px;
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

// 响应式设计
@media (max-width: 768px) {
  .order-summary .summary-info {
    flex-direction: column;
    gap: 10px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .status-info {
      text-align: left;
    }
  }

  .basic-info {
    grid-template-columns: 1fr !important;
  }

  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
