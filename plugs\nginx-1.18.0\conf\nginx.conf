
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type text/html;
	client_max_body_size 0;
	
	# 重要
    map $http_upgrade $connection_upgrade {
      default upgrade;
      '' close;
    }
    # 使用了Nginx的map指令，根据请求头中的“Upgrade”字段值
    # 判断是否需要升级HTTP连接为WebSocket连接
    # 如果没有的话，nginx代理配置websocket会报错

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;
	
	underscores_in_headers  on;

    #gzip  on;
  
    ##### 配置开始 #################
	 
	include	domains.d/nginx.conf;	

    ##### 配置结束 #################

}
