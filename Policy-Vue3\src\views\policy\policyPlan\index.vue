<template>
    <div class="policy-plan-container app-container">
        <!-- 使用 TableList 组件 -->
        <TableList v-if="isTableReady" :columns="tableColumns" :data="applicationList" :loading="tableLoading"
            :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
            operationWidth="280" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
            :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
            @current-change="handleCurrentChange" @size-change="handleSizeChange"
            @selection-change="handleSelectionChange">

            <!-- 申请状态列插槽 -->
            <template #applicationStatus="{ row }">
                <el-tag :type="getStatusTagType(row.applicationStatus)" size="small">
                    {{ getStatusText(row.applicationStatus) }}
                </el-tag>
            </template>

            <!-- 操作列插槽 -->
            <template #menu="{ row }">
                <div class="operation-btns">
                    <el-button type="primary" link @click="handleView(row)">查看</el-button>
                    <el-button type="info" link @click="handleViewMaterials(row)">查看材料</el-button>
                    <el-button type="success" link @click="handleViewRecords(row)">审核记录</el-button>

                    <!-- 一级审核按钮 - 当状态为待初审且用户有初审权限时显示 -->
                    <el-button v-hasPermi="['policy:application:first-review']" v-if="row.applicationStatus === '0'"
                        type="warning" link @click="handleFirstReview(row)">初审</el-button>

                    <!-- 二级审核按钮 - 当状态为待终审且用户有终审权限时显示 -->
                    <el-button v-hasPermi="['policy:application:final-review']" v-if="row.applicationStatus === '3'"
                        type="danger" link @click="handleFinalReview(row)">终审</el-button>
                </div>
            </template>
        </TableList>

        <div v-else class="loading-placeholder">
            <el-empty description="正在加载表格配置..."></el-empty>
        </div>

        <!-- 申请详情弹窗组件 -->
        <ApplicationFormDialog ref="applicationFormDialogRef" :formFields="formFields" :formOption="formOption"
            @submit="handleFormSubmit" @cancel="handleFormCancel" />

        <!-- 审核弹窗组件 -->
        <ReviewDialog ref="reviewDialogRef" @submit="handleReviewSubmit" />

        <!-- 审核记录弹窗组件 -->
        <ApprovalRecordsDialog ref="approvalRecordsDialogRef" />

        <!-- 材料查看弹窗组件 -->
        <MaterialsDialog ref="materialsDialogRef" />
    </div>
</template>

<script setup name="PolicyPlan">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed } from 'vue'
import {
    listPolicyApplication,
    getPolicyApplication,
    delPolicyApplication,
    updatePolicyApplication,
    firstReview,
    finalReview,
    getApprovalRecords
} from "@/api/policy/application"
import { createPolicyApplicationTableOption } from "@/const/policy/application"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import ApplicationFormDialog from './ApplicationFormDialog.vue'
import ReviewDialog from './ReviewDialog.vue'
import ApprovalRecordsDialog from './ApprovalRecordsDialog.vue'
import MaterialsDialog from './MaterialsDialog.vue'

const { proxy } = getCurrentInstance()

const applicationList = ref([])
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([])
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
    dialogWidth: '1000px',
    dialogHeight: '70vh'
})
const tableListRef = ref(null)
const applicationFormDialogRef = ref(null)
const reviewDialogRef = ref(null)
const approvalRecordsDialogRef = ref(null)
const materialsDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        policyName: undefined,
        applicationStatus: undefined,
        applicantUserName: undefined
    }
})

const { queryParams } = toRefs(data)


// 初始化配置
onMounted(async () => {
    await initializeConfig()
    getList()
})

// 初始化配置
const initializeConfig = async () => {
    try {
        const baseOption = createPolicyApplicationTableOption(proxy);
        const mergedConfig = await getCoSyncColumn({
            baseOption,
            proxy
        });

        const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

        tableColumns.value = extractedTableColumns;
        searchableColumns.value = searchColumns;
        formFields.value = extractedFormFields;

        formOption.value = {
            ...formOption.value,
            ...formOptions
        };

        isTableReady.value = true;
    } catch (error) {
        console.error('初始化配置失败:', error);
        proxy.$modal.msgError('初始化配置失败');
    }
}

/** 查询申请列表 */
function getList() {
    tableLoading.value = true;

    // 统一使用一个接口查询所有数据
    listPolicyApplication({ ...queryParams.value, ...searchParams.value }).then(response => {
        applicationList.value = response.rows || [];
        total.value = response.total || 0;
        tableLoading.value = false;
    }).catch(error => {
        console.error('查询申请列表失败:', error);
        applicationList.value = [];
        total.value = 0;
        tableLoading.value = false;
    });
}

// 搜索处理
const handleSearch = (searchData) => {
    searchParams.value = searchData;
    queryParams.value.pageNum = 1;
    getList();
}

// 重置搜索
const resetSearch = () => {
    searchParams.value = {};
    queryParams.value.pageNum = 1;
    getList();
}

// 分页处理
const handleCurrentChange = (page) => {
    queryParams.value.pageNum = page;
    getList();
}

const handleSizeChange = (size) => {
    queryParams.value.pageSize = size;
    queryParams.value.pageNum = 1;
    getList();
}

// 选择条数
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.applicationId)
    single.value = selection.length != 1
    multiple.value = !selection.length
}

// 状态标签类型
const getStatusTagType = (status) => {
    const statusMap = {
        '0': 'warning',  // 待初审
        '1': 'success',  // 初审通过
        '2': 'danger',   // 初审拒绝
        '3': 'warning',  // 待终审
        '4': 'success',  // 终审通过
        '5': 'danger',   // 终审拒绝
        '6': 'info'      // 已完成
    };
    return statusMap[status] || 'info';
}

// 状态文本
const getStatusText = (status) => {
    const statusMap = {
        '0': '待初审',
        '1': '初审通过',
        '2': '初审拒绝',
        '3': '待终审',
        '4': '终审通过',
        '5': '终审拒绝',
        '6': '已完成'
    };
    return statusMap[status] || '未知状态';
}

// 查看申请详情
const handleView = (row) => {
    getPolicyApplication(row.applicationId).then(response => {
        applicationFormDialogRef.value?.openDialog('view', '查看申请详情', response.data)
    })
}

// 编辑申请
const handleEdit = (row) => {
    getPolicyApplication(row.applicationId).then(response => {
        applicationFormDialogRef.value?.openDialog('edit', '编辑申请', response.data)
    })
}

// 删除申请
const handleDelete = (row) => {
    const applicationIds = row.applicationId || ids.value
    proxy.$modal.confirm('是否确认删除申请编号为"' + applicationIds + '"的数据项？').then(function () {
        return delPolicyApplication(applicationIds)
    }).then(() => {
        getList()
        proxy.$modal.msgSuccess("删除成功")
    }).catch(() => { })
}

// 一级审核操作
const handleFirstReview = (row) => {
    reviewDialogRef.value?.openDialog('first', '初始审核', row)
}

// 二级审核操作
const handleFinalReview = (row) => {
    reviewDialogRef.value?.openDialog('final', '终审核', row)
}

// 查看审核记录
const handleViewRecords = (row) => {
    approvalRecordsDialogRef.value?.openDialog(row.applicationId)
}

// 查看申请材料
const handleViewMaterials = (row) => {
    materialsDialogRef.value?.openDialog(row)
}

// 审核提交处理
const handleReviewSubmit = async (payload) => {
    try {
        const { type, data, applicationData } = payload

        const reviewData = {
            applicationId: applicationData.applicationId,
            approvalStatus: data.approvalStatus,
            approvalComment: data.approvalComment
        }

        if (type === 'first') {
            await firstReview(reviewData)
            proxy.$modal.msgSuccess("初审完成")
        } else if (type === 'final') {
            await finalReview(reviewData)
            proxy.$modal.msgSuccess("终审完成")
        }

        reviewDialogRef.value?.onSubmitSuccess()
        getList()
    } catch (error) {
        reviewDialogRef.value?.onSubmitError()
        console.error('审核失败:', error)
    }
}

// 表单提交处理
const handleFormSubmit = async (payload) => {
    try {
        if (payload.type === 'edit') {
            await updatePolicyApplication(payload.data)
            proxy.$modal.msgSuccess("修改成功")
        }

        applicationFormDialogRef.value?.onSubmitSuccess()
        getList()
    } catch (error) {
        applicationFormDialogRef.value?.onSubmitError()
        console.error('提交失败:', error)
    }
}

// 表单取消处理
const handleFormCancel = () => {
    // 可以在这里添加取消逻辑
}
</script>