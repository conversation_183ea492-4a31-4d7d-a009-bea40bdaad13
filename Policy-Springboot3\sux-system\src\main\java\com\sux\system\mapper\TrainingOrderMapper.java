package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.TrainingOrder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 培训订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Mapper
public interface TrainingOrderMapper extends BaseMapper<TrainingOrder>
{
    /**
     * 查询培训订单列表
     * 
     * @param trainingOrder 培训订单
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectTrainingOrderList(TrainingOrder trainingOrder);

    /**
     * 查询培训订单
     * 
     * @param orderId 培训订单主键
     * @return 培训订单
     */
    public TrainingOrder selectTrainingOrderByOrderId(Long orderId);

    /**
     * 新增培训订单
     * 
     * @param trainingOrder 培训订单
     * @return 结果
     */
    public int insertTrainingOrder(TrainingOrder trainingOrder);

    /**
     * 修改培训订单
     * 
     * @param trainingOrder 培训订单
     * @return 结果
     */
    public int updateTrainingOrder(TrainingOrder trainingOrder);

    /**
     * 删除培训订单
     * 
     * @param orderId 培训订单主键
     * @return 结果
     */
    public int deleteTrainingOrderByOrderId(Long orderId);

    /**
     * 批量删除培训订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingOrderByOrderIds(Long[] orderIds);

    /**
     * 校验订单标题是否唯一
     * 
     * @param trainingOrder 培训订单信息
     * @return 结果
     */
    public TrainingOrder checkOrderTitleUnique(TrainingOrder trainingOrder);

    /**
     * 更新订单报名人数
     *
     * @param orderId 订单ID
     * @param increment 增量（正数为增加，负数为减少）
     * @return 结果
     */
    public int updateCurrentParticipants(Long orderId, int increment);

    /**
     * 更新订单当前报名人数为指定值
     *
     * @param orderId 订单ID
     * @param count 当前报名人数
     * @return 结果
     */
    public int updateCurrentParticipantsCount(Long orderId, int count);

    /**
     * 查询即将开始的培训订单（用于提醒）
     * 
     * @param days 提前天数
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectUpcomingTrainingOrders(int days);

    /**
     * 查询已过期的培训订单（报名截止时间已过）
     * 
     * @return 培训订单集合
     */
    public List<TrainingOrder> selectExpiredTrainingOrders();

    /**
     * 统计各状态订单数量
     * 
     * @return 统计结果
     */
    public List<TrainingOrder> selectOrderStatusStatistics();
}
