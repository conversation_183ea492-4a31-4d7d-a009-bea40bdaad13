/* 公用按钮样式 - 统一封装 */
.common-btn,
.custom-btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:not(.el-button--primary):not(.el-button--warning):not(.el-button--info):not(.el-button--success):not(.el-button--danger) {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #6c757d;

        &:hover {
            border-color: #409eff;
            color: #409eff;
            background: #f0f8ff;
            transform: translateY(-1px);
        }
    }

    &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
        border: none;
        color: white;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #7bb8e5 100%);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
        }
    }

    &.el-button--warning {
        background: linear-gradient(135deg, #e6a23c 0%, #f0c040 100%);
        border: none;
        color: white;
        box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);

        &:hover {
            background: linear-gradient(135deg, #eab543 0%, #f3c648 100%);
            box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
            transform: translateY(-1px);
        }
    }

    &.el-button--info {
        background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
        border: none;
        color: white;
        box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);

        &:hover {
            background: linear-gradient(135deg, #9a9da2 0%, #b0b3b8 100%);
            box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4);
            transform: translateY(-1px);
        }
    }

    &.el-button--success {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border: none;
        color: white;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);

        &:hover {
            background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
            transform: translateY(-1px);
        }
    }

    &.el-button--danger {
        background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
        border: none;
        color: white;
        box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

        &:hover {
            background: linear-gradient(135deg, #f78989 0%, #f9a3a4 100%);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
            transform: translateY(-1px);
        }
    }
}

/* 操作按钮样式 */
.op-btn {
    padding: 2px 4px;
    font-size: 11px;
    border-radius: 3px;
    transition: all 0.3s ease;
    flex-shrink: 0;
    min-width: 28px;
    white-space: nowrap;
    font-weight: 500;
    margin-bottom: 1px;
    height: 22px;
    line-height: 1;

    &:hover {
        transform: translateY(-1px);
    }
}

/* 统一按钮样式 - 用于表格等其他位置 */
.unified-btn {
    padding: 8px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    min-width: 80px !important;
    height: 36px !important;

    &:not(.el-button--primary):not(.el-button--warning):not(.el-button--info):not(.el-button--success):not(.el-button--danger) {
        background: #f8f9fa !important;
        border-color: #dee2e6 !important;
        color: #6c757d !important;

        &:hover {
            border-color: #409eff !important;
            color: #409eff !important;
            background: #f0f8ff !important;
            transform: translateY(-1px) !important;
        }
    }

    &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #5dade2 100%) !important;
        border: none !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3) !important;

        &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #7bb8e5 100%) !important;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4) !important;
            transform: translateY(-1px) !important;
        }

        &:active {
            transform: translateY(0) !important;
        }
    }

    &.el-button--warning {
        background: linear-gradient(135deg, #e6a23c 0%, #f0c040 100%) !important;
        border: none !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3) !important;

        &:hover {
            background: linear-gradient(135deg, #eab543 0%, #f3c648 100%) !important;
            box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4) !important;
            transform: translateY(-1px) !important;
        }
    }

    &.el-button--info {
        background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%) !important;
        border: none !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3) !important;

        &:hover {
            background: linear-gradient(135deg, #9a9da2 0%, #b0b3b8 100%) !important;
            box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4) !important;
            transform: translateY(-1px) !important;
        }
    }

    &.el-button--success {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
        border: none !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3) !important;

        &:hover {
            background: linear-gradient(135deg, #85ce61 0%, #95d475 100%) !important;
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4) !important;
            transform: translateY(-1px) !important;
        }
    }

    &.el-button--danger {
        background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%) !important;
        border: none !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;

        &:hover {
            background: linear-gradient(135deg, #f78989 0%, #f9a3a4 100%) !important;
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4) !important;
            transform: translateY(-1px) !important;
        }
    }
}

/* 操作按钮容器样式 */
.operation-btns {
    display: flex;
    gap: 1px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    flex-wrap: wrap;
    align-items: flex-start;
}

/* 价格展示样式 */
.price-cell {
    width: 100%;

    .price-text {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 6px;
        background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
        border: 1px solid #b3e5b4;
        color: #52c41a;
        font-weight: 600;
        font-size: 13px;
    }

    .no-price {
        color: #909399;
        font-style: italic;
    }
}

/* 搜索表单输入框统一样式 */
.search-form-item {
    width: 150px !important;
    
    .el-input,
    .el-select,
    .el-date-editor,
    .el-tree-select,
    .el-cascader {
        width: 100% !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
        
        .el-input__wrapper {
            border-radius: 6px !important;
            box-shadow: 0 0 0 1px #dcdfe6 inset !important;
            transition: all 0.3s ease !important;
            
            &:hover {
                box-shadow: 0 0 0 1px #c0c4cc inset !important;
            }
            
            &.is-focus {
                box-shadow: 0 0 0 1px #409eff inset !important;
            }
        }
        
        &.el-select {
            .el-select__wrapper {
                border-radius: 6px !important;
                box-shadow: 0 0 0 1px #dcdfe6 inset !important;
                transition: all 0.3s ease !important;
                
                &:hover {
                    box-shadow: 0 0 0 1px #c0c4cc inset !important;
                }
                
                &.is-focus {
                    box-shadow: 0 0 0 1px #409eff inset !important;
                }
            }
        }
        
        &.el-tree-select {
            .el-tree-select__wrapper {
                border-radius: 6px !important;
                box-shadow: 0 0 0 1px #dcdfe6 inset !important;
                transition: all 0.3s ease !important;
                
                &:hover {
                    box-shadow: 0 0 0 1px #c0c4cc inset !important;
                }
                
                &.is-focus {
                    box-shadow: 0 0 0 1px #409eff inset !important;
                }
            }
        }
    }
}

/* 搜索表单输入框 - 小尺寸 */
.search-form-item--small {
    width: 120px !important;
    min-width: 140px !important;
    max-width: 180px !important;
}

/* 搜索表单输入框 - 大尺寸 */
.search-form-item--large {
    width: 260px !important;
    min-width: 240px !important;
    max-width: 300px !important;
}

/* 搜索表单日期范围选择器 */
.search-form-item--date-range {
    width: 240px !important;
    min-width: 220px !important;
    max-width: 280px !important;
}
