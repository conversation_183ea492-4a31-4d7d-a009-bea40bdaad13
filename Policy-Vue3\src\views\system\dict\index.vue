<template>
    <div class="dict-container app-container">
        <!-- 使用 TableList 组件 -->
        <TableList v-if="isTableReady" :columns="tableColumns" :data="tableData" :loading="tableLoading"
            :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
            operationWidth="200" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
            :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
            @current-change="handleCurrentChange" @size-change="handleSizeChange"
            @selection-change="handleSelectionChange">

            <!-- 左侧按钮插槽 -->
            <template #menu-left>
                <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['system:dict:add']">
                    新 增
                </el-button>
                <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['system:dict:export']">
                    导 出
                </el-button>
                <el-button type="danger" class="custom-btn" plain @click="handleRefreshCache"
                    v-hasPermi="['system:dict:remove']">
                    刷 新 缓 存
                </el-button>
            </template>

            <!-- 字典类型列插槽 - 显示为链接 -->
            <template #dictType="{ row }">
                <router-link :to="'/system/dict-data/index/' + row.dictId" class="link-type">
                    <span>{{ row.dictType }}</span>
                </router-link>
            </template>

            <!-- 操作列插槽 -->
            <template #menu="{ row }">
                <div class="operation-btns">
                    <el-button type="primary" link class="table-action-btn" @click="handleView(row)">查看</el-button>
                    <el-button type="primary" link class="table-action-btn" @click="handleEdit(row)"
                        v-hasPermi="['system:dict:edit']">编辑</el-button>
                    <el-button type="danger" link class="table-action-btn" @click="handleDelete(row)"
                        v-hasPermi="['system:dict:remove']">删除</el-button>
                </div>
            </template>
        </TableList>

        <div v-else class="loading-placeholder">
            <el-empty description="正在加载表格配置..."></el-empty>
        </div>

        <!-- 表单弹窗组件 -->
        <DictFormDialog ref="dictFormDialogRef" :formFields="formFields" :formOption="formOption"
            @submit="handleFormSubmit" @cancel="handleFormCancel" />
    </div>
</template>

<script setup name="Dict">
import { ref, reactive, onMounted, getCurrentInstance, computed } from 'vue';
import { createDictTypeTableOption } from "@/const/dict/index";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import { listType, getType, delType, addType, updateType, refreshCache } from "@/api/system/dict/type";
import useDictStore from '@/store/modules/dict';
import TableList from '@/components/TableList/index.vue';
import DictFormDialog from './DictFormDialog.vue';

const { proxy } = getCurrentInstance();
const tableColumns = ref([]);
const searchableColumns = ref([]);
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
    dialogWidth: '600px',
    dialogHeight: '60vh'
});
const tableListRef = ref(null);
const dictFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});
const dateRange = ref([]);

// 选择相关
const selectedRows = ref([]);
const single = ref(true);
const multiple = ref(true);

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 初始化时获取数据
onMounted(async () => {
    await initializeConfig();
});

// 初始化配置
const initializeConfig = async () => {
    try {
        // 获取基础配置
        const baseOption = createDictTypeTableOption(proxy);

        // 使用工具类获取合并后的配置
        const mergedConfig = await getCoSyncColumn({
            baseOption,
            proxy
        });

        // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
        const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

        // 设置表格和搜索配置
        tableColumns.value = extractedTableColumns;
        searchableColumns.value = searchColumns;

        // 设置表单字段配置
        formFields.value = extractedFormFields;

        // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
        formOption.value = {
            ...formOption.value, // 保留默认配置
            ...formOptions       // 使用从配置文件中提取的完整选项
        };

        isTableReady.value = true;

        // 加载表格数据
        loadData();
    } catch (error) {
        isTableReady.value = false;
        console.error('初始化配置失败:', error);
    }
};

// 加载表格数据
const loadData = () => {
    tableLoading.value = true;

    const queryParams = {
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        ...searchParams.value
    };

    listType(proxy.addDateRange(queryParams, dateRange.value)).then(response => {
        tableData.value = response.rows;
        total.value = response.total;
        tableLoading.value = false;
        updateTablePagination();
    }).catch(() => {
        tableLoading.value = false;
    });
};

// 更新表格分页信息
const updateTablePagination = () => {
    if (tableListRef.value && tableListRef.value.page) {
        tableListRef.value.page.total = total.value;
        tableListRef.value.page.currentPage = currentPage.value;
        tableListRef.value.page.pageSize = pageSize.value;
    }
};

// 处理搜索
const handleSearch = (params) => {
    // 处理日期范围搜索
    if (params.createTime && Array.isArray(params.createTime)) {
        dateRange.value = params.createTime;
        delete params.createTime;
    } else {
        dateRange.value = [];
    }

    searchParams.value = params || {};
    currentPage.value = 1;
    loadData();
};

// 重置搜索
const resetSearch = () => {
    searchParams.value = {};
    dateRange.value = [];
    currentPage.value = 1;
    loadData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    currentPage.value = page;
    loadData();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1;
    loadData();
};

// 处理选择变化
const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
};

// 查看
const handleView = (row) => {
    dictFormDialogRef.value?.openDialog('view', '查看字典', row);
};

// 编辑
const handleEdit = (row) => {
    const dictId = row ? row.dictId : selectedRows.value[0]?.dictId;
    if (!dictId) return;

    getType(dictId).then(response => {
        dictFormDialogRef.value?.openDialog('edit', '编辑字典', response.data);
    });
};

// 新增
const handleAdd = () => {
    dictFormDialogRef.value?.openDialog('add', '新增字典', { status: "0" });
};

// 批量修改
const handleUpdate = () => {
    if (selectedRows.value.length === 1) {
        handleEdit();
    } else {
        proxy.$modal.msgWarning("请选择一条记录进行修改");
    }
};

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
    try {
        if (payload.type === 'add') {
            await addType(payload.data);
            proxy.$modal.msgSuccess("添加成功");
        } else if (payload.type === 'edit') {
            await updateType(payload.data);
            proxy.$modal.msgSuccess("修改成功");
        }

        // 通知子组件提交成功
        dictFormDialogRef.value?.onSubmitSuccess();
        loadData();
    } catch (error) {
        // 通知子组件提交失败
        dictFormDialogRef.value?.onSubmitError();
        console.error('提交失败:', error);
    }
};

// 处理表单取消事件
const handleFormCancel = () => {
    // 可以在这里添加取消逻辑
};

// 处理删除
const handleDelete = (row) => {
    const dictIds = row ? row.dictId : selectedRows.value.map(item => item.dictId);
    const dictNames = row ? row.dictName : selectedRows.value.map(item => item.dictName).join("、");

    proxy.$modal.confirm('是否确认删除字典【' + dictNames + '】的数据项？').then(() => {
        return delType(dictIds);
    }).then(() => {
        loadData();
        proxy.$modal.msgSuccess("删除成功");
    });
};

// 导出
const handleExport = () => {
    const queryParams = {
        ...searchParams.value
    };
    proxy.download("system/dict/type/export",
        proxy.addDateRange(queryParams, dateRange.value),
        `dict_${new Date().getTime()}.xlsx`);
};

// 刷新缓存
const handleRefreshCache = () => {
    refreshCache().then(() => {
        proxy.$modal.msgSuccess("刷新成功");
        useDictStore().cleanDict();
    });
};
</script>

<style lang="scss" scoped>
.dict-container {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    :deep(.el-table) {
        margin-top: 15px;
        width: 100%;
        max-width: 100%;
    }

    :deep(.el-pagination) {
        justify-content: flex-end;
    }

    .loading-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
    }

    .operation-btns {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .link-type {
        color: #409eff;
        text-decoration: none;

        &:hover {
            color: #66b1ff;
            text-decoration: underline;
        }
    }
}
</style>
