<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth || '1000px'" 
    :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    
    <div class="form-container" :style="{ maxHeight: formOption.dialogHeight || '70vh', overflowY: 'auto' }">
      <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="formOption.labelWidth || '120px'">
        <el-row :gutter="20">
          <template v-for="field in formFields" :key="field.prop">
            <!-- 分隔线 -->
            <el-col v-if="field.divider" :span="24" class="divider-col">
              <el-divider content-position="left">{{ field.label }}</el-divider>
            </el-col>
            
            <!-- 普通字段 -->
            <el-col v-else-if="shouldShowField(field)" :span="field.span || 24">
              <el-form-item :label="field.label" :prop="field.prop">
                <!-- 输入框 -->
                <el-input
                  v-if="!field.type || field.type === 'input'"
                  v-model="formData[field.prop]"
                  :placeholder="'请输入' + field.label"
                  :disabled="isViewMode || field.disabled"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                />
                
                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :placeholder="'请输入' + field.label"
                  :disabled="isViewMode || field.disabled"
                  :rows="field.minRows || 3"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                />
                
                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formData[field.prop]"
                  :placeholder="'请输入' + field.label"
                  :disabled="isViewMode || field.disabled"
                  :min="field.min"
                  :max="field.max"
                  :precision="field.precision"
                  style="width: 100%"
                />
                
                <!-- 选择器 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="'请选择' + field.label"
                  :disabled="isViewMode || field.disabled"
                  style="width: 100%"
                  clearable
                >
                  <el-option
                    v-for="option in field.dicData"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                
                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'datetime'"
                  v-model="formData[field.prop]"
                  type="datetime"
                  :placeholder="'请选择' + field.label"
                  :disabled="isViewMode || field.disabled"
                  :format="field.format || 'YYYY-MM-DD HH:mm:ss'"
                  :value-format="field.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                  style="width: 100%"
                />
                
                <!-- 开关 -->
                <el-switch
                  v-else-if="field.type === 'switch'"
                  v-model="formData[field.prop]"
                  :disabled="isViewMode || field.disabled"
                  :active-value="field.activeValue || '1'"
                  :inactive-value="field.inactiveValue || '0'"
                />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button v-if="!isViewMode" type="primary" :loading="submitLoading" @click="handleSubmit">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="TrainingOrderFormDialog">
import { ref, reactive, computed, watch, nextTick, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// 定义组件属性
const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('')
const formData = reactive({})
const formRules = ref({})
const submitLoading = ref(false)
const formRef = ref(null)
const originalData = ref({})

// 计算是否为查看模式
const isViewMode = computed(() => dialogType.value === 'view')

// 监听表单字段变化，生成验证规则
watch(() => props.formFields, (newFields) => {
  const rules = {}
  newFields.forEach(field => {
    if (field.rules && field.prop) {
      rules[field.prop] = field.rules
    }
  })
  formRules.value = rules
}, { immediate: true, deep: true })

// 判断字段是否应该显示
const shouldShowField = (field) => {
  if (dialogType.value === 'add') {
    return field.addDisplay !== false
  } else if (dialogType.value === 'edit') {
    return field.editDisplay !== false
  } else if (dialogType.value === 'view') {
    return field.viewDisplay !== false
  }
  return true
}

// 打开弹窗
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  // 设置表单数据
  Object.assign(formData, data)
  originalData.value = { ...data }
  
  dialogVisible.value = true
  
  // 重置表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      // 表单验证通过，进行业务逻辑验证
      if (!validateBusinessRules()) {
        return
      }
      
      submitLoading.value = true
      
      // 发送提交事件
      emit('submit', {
        type: dialogType.value,
        data: { ...formData }
      })
    }
  })
}

// 业务逻辑验证
const validateBusinessRules = () => {
  // 验证时间逻辑
  if (formData.startDate && formData.endDate) {
    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      proxy.$modal.msgError('结束时间必须晚于开始时间')
      return false
    }
  }
  
  if (formData.registrationDeadline && formData.startDate) {
    if (new Date(formData.registrationDeadline) >= new Date(formData.startDate)) {
      proxy.$modal.msgError('报名截止时间必须早于培训开始时间')
      return false
    }
  }
  
  // 验证人数逻辑
  if (formData.currentParticipants && formData.maxParticipants) {
    if (formData.currentParticipants > formData.maxParticipants) {
      proxy.$modal.msgError('当前报名人数不能超过最大参与人数')
      return false
    }
  }
  
  return true
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
  resetForm()
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  originalData.value = {}
  
  nextTick(() => {
    if (formRef.value) {
      formRef.value.resetFields()
      formRef.value.clearValidate()
    }
  })
}

// 暴露方法给父组件
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError,
  resetForm
})
</script>

<style lang="scss" scoped>
.form-container {
  padding: 0 10px;
}

.divider-col {
  margin: 10px 0;
  
  .el-divider {
    margin: 15px 0;
    
    :deep(.el-divider__text) {
      font-weight: 600;
      color: #409eff;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>
