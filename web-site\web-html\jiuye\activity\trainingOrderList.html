<!DOCTYPE html>
<html>
<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>培训订单列表-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- swiper css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/swiper/swiper.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202505271645" />
    <link rel="stylesheet" type="text/css" href="css/activityDetail.css?v=202502281010" />
    <link rel="stylesheet" type="text/css" href="css/layout-fix.css?v=202507221200" />
    <link rel="stylesheet" type="text/css" href="css/trainingOrderList.css?v=202507231200" />

    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <!-- banner -->
    <div class="bannerBox pr">
        <!-- ko if:bagnnerList().length>0 -->
        <div class="bannerSlide" data-bind="foreach:bagnnerList">
            <img src="./image/new_zhdBanner.jpg" data-bind="attr:{src:fullPath}">
        </div>
        <div class="hd" data-bind="visible:bagnnerList().length>1">
            <ul></ul>
        </div>
        <!-- /ko -->
        <!-- ko if:bagnnerList().length==0 -->
        <div class="bannerSlide">
            <img src="./image/new_zhdBanner.jpg">
        </div>
        <!-- /ko -->
    </div>
    <div class="pageBg">
        <div class="bgBox">
            <!-- 浏览路径 start -->
            <div class="pathbox2">
            </div>
            <!-- 浏览路径 end -->
            <!-- 主题内容 start -->
            <div class="conAuto3 mb30">
                <div class="main-layout-container">
                    <!-- 主要内容区域 -->
                    <div class="training-order-list-page-new">
                        <!-- 页面标题区域 -->
                        <div class="page-header-new">
                            <div class="header-content-new">
                                <div class="title-section-new">
                                    <h1 class="page-title-new">培训订单列表</h1>
                                    <div class="title-meta-new">
                                        <span class="meta-tag-new">📋</span>
                                        <span class="subtitle-text">发现优质培训机会，提升职业技能</span>
                                    </div>
                                </div>
                                <div class="header-actions-new">
                                    <button class="action-btn-new primary" onclick="refreshData()">
                                        <span class="btn-icon-new">🔄</span>
                                        <span class="btn-text-new">刷新数据</span>
                                    </button>
                                    <button class="action-btn-new secondary" onclick="exportData()">
                                        <span class="btn-icon-new">📊</span>
                                        <span class="btn-text-new">导出数据</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索筛选区域 -->
                        <div class="search-filters-new">
                            <div class="filter-container-new">
                                <div class="filter-title-new">
                                    <span class="filter-icon-new">🔍</span>
                                    <span class="filter-text-new">筛选条件</span>
                                </div>
                                <div class="filter-row-new">
                                    <div class="filter-item-new">
                                        <label class="filter-label-new">培训类型</label>
                                        <select id="trainingType" class="filter-select-new">
                                            <option value="">全部类型</option>
                                            <option value="技术培训">技术培训</option>
                                            <option value="管理培训">管理培训</option>
                                            <option value="职业技能">职业技能</option>
                                            <option value="安全培训">安全培训</option>
                                            <option value="合规培训">合规培训</option>
                                            <option value="其他">其他</option>
                                        </select>
                                    </div>
                                    <div class="filter-item-new">
                                        <label class="filter-label-new">培训级别</label>
                                        <select id="trainingLevel" class="filter-select-new">
                                            <option value="">全部级别</option>
                                            <option value="初级">初级</option>
                                            <option value="中级">中级</option>
                                            <option value="高级">高级</option>
                                            <option value="专家级">专家级</option>
                                        </select>
                                    </div>
                                    <div class="filter-actions-new">
                                        <button class="btn-search-new" onclick="handleSearch()">
                                            <span class="btn-icon-new">🔍</span>
                                            <span class="btn-text-new">搜索</span>
                                        </button>
                                        <button class="btn-reset-new" onclick="refreshData()">
                                            <span class="btn-icon-new">🔄</span>
                                            <span class="btn-text-new">重置</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 统计信息区域 -->
                        <div class="stats-section-new">
                            <div class="stats-container-new">
                                <div class="stat-item-new">
                                    <div class="stat-icon-new">📊</div>
                                    <div class="stat-content-new">
                                        <div class="stat-number-new" id="totalCount">0</div>
                                        <div class="stat-label-new">培训订单</div>
                                    </div>
                                </div>
                                <div class="stat-divider-new"></div>
                                <div class="stat-item-new">
                                    <div class="stat-icon-new">⏰</div>
                                    <div class="stat-content-new">
                                        <div class="stat-number-new" id="lastUpdate">--</div>
                                        <div class="stat-label-new">最后更新</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 订单列表区域 -->
                        <div class="order-list-container-new">
                            <div class="order-grid-new" id="orderGrid">
                                <!-- 订单卡片将通过JavaScript动态生成 -->
                            </div>

                            <!-- 无数据提示 -->
                            <div class="no-data-new" id="noDataTip" style="display: none;">
                                <div class="no-data-content-new">
                                    <div class="no-data-icon-new">📋</div>
                                    <div class="no-data-text-new">
                                        <h3>暂无培训订单</h3>
                                        <p>请尝试调整筛选条件或稍后再试</p>
                                    </div>
                                    <button class="retry-btn-new" onclick="refreshData()">
                                        <span class="btn-icon-new">🔄</span>
                                        <span class="btn-text-new">重新加载</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 加载状态 -->
                            <div class="loading-state-new" id="loadingState" style="display: none;">
                                <div class="loading-content-new">
                                    <div class="loading-spinner-new"></div>
                                    <p class="loading-text-new">正在加载培训订单...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主题内容 end -->
            </div>
        </div>
    </div>
    <!-- main end -->

    <!-- 底部信息条 -->
    <div class="bottomBar">
        <div class="conAuto1400">
            <div class="bottomContent">
                <div class="bottomLeft">
                    <div class="bottomLogo">
                        <span class="logoText">就业培训平台</span>
                        <span class="logoSubtext">Employment Training Platform</span>
                    </div>
                    <div class="bottomInfo">
                        <p class="info-text">致力于为求职者提供专业的就业培训服务</p>
                        <p class="info-text">打造高质量的职业技能提升平台</p>
                    </div>
                </div>
                <div class="bottomRight">
                    <div class="contact-info">
                        <p class="contact-title">联系我们</p>
                        <p class="contact-item">服务热线：400-123-4567</p>
                        <p class="contact-item">邮箱：<EMAIL></p>
                    </div>
                    <div class="service-info">
                        <p class="service-title">服务时间</p>
                        <p class="service-item">周一至周五 9:00-18:00</p>
                        <p class="service-item">周六至周日 9:00-17:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <script src="../public/plugins/swiper/swiper.min5.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/indexs.js?v=202505280848" type="text/javascript" charset="utf-8"></script>

    <script>
        // 全局变量
        var orderList = [];
        var searchParams = {
            trainingType: '',
            trainingLevel: '',
            orderStatus: '1' // 默认显示已发布的
        };
        var isPageReady = false;

        // 获取状态文本
        function getStatusText(status) {
            var statusMap = {
                '0': '草稿',
                '1': '发布',
                '2': '进行中',
                '3': '已完成',
                '4': '已取消'
            };
            return '';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            var classMap = {
                '0': 'status-draft',
                '1': 'status-published',
                '2': 'status-ongoing',
                '3': 'status-completed',
                '4': 'status-cancelled'
            };
            return classMap[status] || '';
        }

        // 跳转到订单详情
        function goOrderDetail(orderId) {
            window.open('trainingOrderDetail.html?id=' + orderId);
        }

        // 搜索处理
        function handleSearch() {
            searchParams.trainingType = document.getElementById('trainingType').value;
            searchParams.trainingLevel = document.getElementById('trainingLevel').value;
            refreshData();
        }

        // 重置处理
        function handleReset() {
            document.getElementById('trainingType').value = '';
            document.getElementById('trainingLevel').value = '';
            document.getElementById('orderStatus').value = '1';
            searchParams.trainingType = '';
            searchParams.trainingLevel = '';
            searchParams.orderStatus = '1';
            loadOrderList();
        }

        // 原生Ajax请求函数
        function customAjaxRequest(url, params, callback) {
            var baseUrl = 'http://localhost:80/sux-admin/';

            // 构建查询参数
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var xhr = new XMLHttpRequest();
            xhr.open('GET', baseUrl + url + queryString, true);
            xhr.timeout = 30000;
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback && typeof callback === 'function') {
                                callback(response);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            if (callback && typeof callback === 'function') {
                                callback({
                                    code: -1,
                                    msg: '解析响应数据失败',
                                    rows: [],
                                    total: 0
                                });
                            }
                        }
                    } else {
                        console.error('请求失败:', xhr.status, xhr.statusText);
                        if (callback && typeof callback === 'function') {
                            callback({
                                code: -1,
                                msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                                rows: [],
                                total: 0
                            });
                        }
                    }
                }
            };

            xhr.ontimeout = function() {
                console.error('请求超时');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求超时',
                        rows: [],
                        total: 0
                    });
                }
            };

            xhr.onerror = function() {
                console.error('请求发生错误');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '网络错误',
                        rows: [],
                        total: 0
                    });
                }
            };

            xhr.send();
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingState').style.display = 'flex';
            document.getElementById('orderGrid').style.display = 'none';
            document.getElementById('noDataTip').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
        }

        // 渲染订单列表
        function renderOrderList(orders) {
            var orderGrid = document.getElementById('orderGrid');
            var noDataTip = document.getElementById('noDataTip');

            if (!orders || orders.length === 0) {
                orderGrid.style.display = 'none';
                noDataTip.style.display = 'flex';
                updateListStats(0);
                return;
            }

            var html = '';
            orders.forEach(function(order) {
                var statusText = getStatusText(order.orderStatus);
                var statusClass = getStatusClass(order.orderStatus);
                var feeText = order.trainingFee && order.trainingFee > 0 ? '￥' + parseFloat(order.trainingFee).toFixed(2) : '免费';
                var timeText = order.startDate ? formatDateTime(order.startDate) : '--';
                var participantsText = (order.currentParticipants || 0) + '/' + (order.maxParticipants || 0) + '人';
                var progressPercent = order.maxParticipants > 0 ? (order.currentParticipants / order.maxParticipants * 100) : 0;

                html += `
                    <div class="order-card-new" onclick="goOrderDetail(${order.orderId})">
                        <!-- 卡片头部 -->
                        <div class="card-header-new">
                            <h3 class="order-title-new" title="${order.orderTitle || ''}">${order.orderTitle || '--'}</h3>
                            <div class="order-meta-tags-new">
                                <span class="meta-tag-new type-tag-new">${order.trainingType || '--'}</span>
                                <span class="meta-tag-new level-tag-new">${order.trainingLevel || '--'}</span>
                            </div>
                        </div>

                        <!-- 卡片内容 -->
                        <div class="card-content-new">
                            <div class="info-grid-new">
                                <div class="info-item-new">
                                    <div class="info-icon-new">🕒</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">培训时间</div>
                                        <div class="info-value-new">${timeText.split(' ')[0] || '--'}</div>
                                    </div>
                                </div>
                                <div class="info-item-new">
                                    <div class="info-icon-new">⏱️</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">时长</div>
                                        <div class="info-value-new">${order.trainingDuration ? order.trainingDuration + 'h' : '--'}</div>
                                    </div>
                                </div>
                                <div class="info-item-new">
                                    <div class="info-icon-new">�</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">地址</div>
                                        <div class="info-value-new">${(order.trainingAddress || '--').substring(0, 15)}${(order.trainingAddress || '').length > 15 ? '...' : ''}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 报名进度 -->
                            <div class="progress-section-new">
                                <div class="progress-header-new">
                                    <span class="progress-label-new">报名进度</span>
                                    <span class="progress-stats-new">${participantsText}</span>
                                </div>
                                <div class="progress-bar-container-new">
                                    <div class="progress-bar-bg-new">
                                        <div class="progress-bar-fill-new" style="width: ${progressPercent}%"></div>
                                    </div>
                                    <div class="progress-percentage-new">${Math.round(progressPercent)}%</div>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片底部操作 -->
                        <div class="card-footer-new">
                            <button class="btn-view-detail-new" onclick="event.stopPropagation(); goOrderDetail(${order.orderId})">
                                <span class="btn-icon-new">👁️</span>
                                <span class="btn-text-new">详情</span>
                            </button>
                            <div class="card-price-new">${feeText}</div>
                        </div>
                    </div>
                `;
            });

            orderGrid.innerHTML = html;
            orderGrid.style.display = 'grid';
            noDataTip.style.display = 'none';
            updateListStats(orders.length);
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            if (!dateStr) return '--';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr;

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');

            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
        }

        // 更新列表统计信息
        function updateListStats(count) {
            document.getElementById('totalCount').textContent = count;
            var now = new Date();
            var timeString = String(now.getHours()).padStart(2, '0') + ':' +
                           String(now.getMinutes()).padStart(2, '0');
            document.getElementById('lastUpdate').textContent = timeString;
        }

        // 加载订单列表
        function loadOrderList() {
            showLoading();

            var obj = {
                pageSize: 100,  // 固定查询100条
                pageNum: 1,     // 固定第一页
                trainingType: searchParams.trainingType,
                trainingLevel: searchParams.trainingLevel,
                orderStatus: searchParams.orderStatus
            };

            console.log('请求参数:', obj);

            customAjaxRequest('public/training/order/list', obj, function(data){
                hideLoading();
                console.log('API响应:', data);

                if(data.code == 0 || data.code == 200) {
                    var rows = data.rows || data.data || [];
                    console.log('获取到的订单数据:', rows);

                    orderList = rows;
                    renderOrderList(rows);
                } else {
                    console.error('获取订单列表失败：', data.msg || data.message);

                    // 如果API失败，加载模拟数据用于测试
                    loadMockData();
                }
            });
        }

        // 刷新数据
        function refreshData() {
            showToast('正在刷新数据...', 'info');
            loadOrderList();
        }
        refreshData();

        // 导出数据
        function exportData() {
            if (orderList.length === 0) {
                showToast('暂无数据可导出', 'info');
                return;
            }

            // 简单的CSV导出
            var csvContent = "订单标题,培训类型,培训级别,培训时长,最大参与人数,当前参与人数,培训费用,开始时间,培训地址,订单状态\n";
            orderList.forEach(function(order) {
                var row = [
                    order.orderTitle || '',
                    order.trainingType || '',
                    order.trainingLevel || '',
                    order.trainingDuration || '',
                    order.maxParticipants || '',
                    order.currentParticipants || '',
                    order.trainingFee || '',
                    order.startDate || '',
                    order.trainingAddress || '',
                    getStatusText(order.orderStatus)
                ].map(function(field) {
                    return '"' + String(field).replace(/"/g, '""') + '"';
                }).join(',');
                csvContent += row + "\n";
            });

            var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            var link = document.createElement("a");
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "培训订单列表_" + new Date().toISOString().slice(0,10) + ".csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('数据导出成功', 'success');
        }

        // 显示提示消息
        function showToast(message, type) {
            var toastClass = type === 'success' ? 'toast-success' : 'toast-info';
            var toast = document.createElement('div');
            toast.className = 'toast ' + toastClass;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(function() {
                toast.classList.add('show');
            }, 100);

            setTimeout(function() {
                toast.classList.remove('show');
                setTimeout(function() {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }

        // 模拟数据用于测试
        function loadMockData() {
            console.log('加载模拟数据');
            var mockData = [
                {
                    orderId: 1,
                    orderTitle: 'Java高级开发工程师培训',
                    trainingType: '技术培训',
                    trainingLevel: '高级',
                    trainingDuration: 120,
                    maxParticipants: 30,
                    currentParticipants: 15,
                    trainingFee: 5800.00,
                    startDate: '2025-08-01 09:00:00',
                    trainingAddress: '青岛市市南区香港中路10号',
                    orderStatus: '1'
                },
                {
                    orderId: 2,
                    orderTitle: 'Python数据分析师培训',
                    trainingType: '技术培训',
                    trainingLevel: '初级',
                    trainingDuration: 80,
                    maxParticipants: 25,
                    currentParticipants: 8,
                    trainingFee: 3800.00,
                    startDate: '2025-08-05 09:00:00',
                    trainingAddress: '青岛市崂山区海尔路178号',
                    orderStatus: '1'
                },
                {
                    orderId: 3,
                    orderTitle: '企业管理与领导力提升',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingDuration: 40,
                    maxParticipants: 20,
                    currentParticipants: 12,
                    trainingFee: 4500.00,
                    startDate: '2025-07-30 09:00:00',
                    trainingAddress: '青岛市市北区辽宁路263号',
                    orderStatus: '2'
                },
                {
                    orderId: 4,
                    orderTitle: '财务分析与预算管理',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingDuration: 50,
                    maxParticipants: 25,
                    currentParticipants: 15,
                    trainingFee: 3500.00,
                    startDate: '2025-08-18 09:00:00',
                    trainingAddress: '青岛市市南区山东路9号',
                    orderStatus: '1'
                }
            ];

            orderList = mockData;
            renderOrderList(mockData);
            console.log('模拟数据已设置:', orderList);
        }

        // 初始化页面
        function initPage() {
            console.log('开始初始化页面...');
            // 设置默认值
            searchParams.orderStatus = '1';

            // 标记页面已准备好
            isPageReady = true;

            console.log('页面初始化完成，开始加载数据...');
            // 加载数据
            loadOrderList();
        }

        // 确保在所有资源加载完成后初始化
        window.addEventListener('load', function() {
            console.log('Window load event triggered');
            // 公用模块html
            if (typeof headerBar === 'function') {
                headerBar();
            }
            if (typeof footerBar === 'function') {
                footerBar();
            }

            // 初始化页面
            initPage();
        });

    </script>
</body>
</html>
