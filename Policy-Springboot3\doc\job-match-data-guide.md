# 招聘匹配系统数据创建指南

## 概述

本指南将帮助您创建测试数据，以便测试招聘信息与零工的匹配功能。匹配算法基于以下关键因素：

1. **地理位置匹配** (权重40%) - 工作地点与零工所在地的匹配度
2. **工作类型匹配** (权重25%) - 全职、兼职、临时工等类型匹配
3. **技能匹配** (权重20%) - 招聘要求技能与零工技能的匹配
4. **薪资匹配** (权重15%) - 薪资范围与期望薪资的重叠度

## 招聘信息数据示例

### 1. IT技术类招聘

```sql
INSERT INTO job_posting (
    job_title, job_description, job_type, job_category, work_location,
    salary_min, salary_max, salary_type, skills_required,
    work_experience_required, education_required, company_name,
    urgency_level, positions_available, status, publisher_user_id,
    create_time, update_time
) VALUES 
(
    'Java开发工程师',
    '负责公司核心业务系统开发，要求熟练掌握Spring Boot、MyBatis等技术',
    '全职',
    'IT技术',
    '青岛市市南区',
    8000,
    15000,
    '月',
    'Java,Spring Boot,MySQL,Redis',
    '3-5年',
    '本科',
    '青岛科技有限公司',
    'normal',
    2,
    'published',
    1,
    NOW(),
    NOW()
),
(
    '前端开发实习生',
    '协助前端页面开发，学习Vue.js和React框架',
    '兼职',
    'IT技术',
    '青岛市崂山区',
    3000,
    5000,
    '月',
    'Vue.js,JavaScript,HTML,CSS',
    '无要求',
    '大专',
    '青岛互联网公司',
    'high',
    3,
    'published',
    1,
    NOW(),
    NOW()
);
```

### 2. 服务业招聘

```sql
INSERT INTO job_posting (
    job_title, job_description, job_type, job_category, work_location,
    salary_min, salary_max, salary_type, skills_required,
    work_experience_required, education_required, company_name,
    urgency_level, positions_available, status, publisher_user_id,
    create_time, update_time
) VALUES 
(
    '餐厅服务员',
    '负责餐厅日常服务工作，包括点餐、上菜、收银等',
    '兼职',
    '餐饮服务',
    '青岛市市北区',
    20,
    25,
    '小时',
    '服务意识,沟通能力,责任心',
    '无要求',
    '不限',
    '青岛美食餐厅',
    'urgent',
    5,
    'published',
    1,
    NOW(),
    NOW()
),
(
    '保洁员',
    '负责办公楼日常清洁工作',
    '临时工',
    '保洁清洁',
    '青岛市李沧区',
    150,
    200,
    '日',
    '细心,勤劳,责任心',
    '无要求',
    '不限',
    '青岛物业管理公司',
    'normal',
    10,
    'published',
    1,
    NOW(),
    NOW()
);
```

## 零工信息数据示例

### 1. IT技术零工

```sql
INSERT INTO worker_profile (
    user_id, real_name, nickname, gender, age, phone, email,
    current_location, work_locations, education_level, work_experience_years,
    work_categories, job_types_preferred, skills,
    salary_expectation_min, salary_expectation_max, salary_expectation_type,
    availability_status, self_introduction, status, create_time, update_time
) VALUES 
(
    2,
    '张三',
    '技术小张',
    'male',
    28,
    '13800138001',
    '<EMAIL>',
    '青岛市市南区',
    '青岛市市南区,青岛市崂山区',
    '本科',
    5,
    'IT技术',
    '全职,兼职',
    'Java,Spring Boot,MySQL,Vue.js,JavaScript',
    7000,
    12000,
    '月',
    'available',
    '5年Java开发经验，熟悉前后端开发',
    'active',
    NOW(),
    NOW()
),
(
    3,
    '李四',
    '前端李',
    'female',
    24,
    '13800138002',
    '<EMAIL>',
    '青岛市崂山区',
    '青岛市崂山区,青岛市市北区',
    '大专',
    2,
    'IT技术',
    '兼职,临时工',
    'Vue.js,React,JavaScript,HTML,CSS',
    3000,
    6000,
    '月',
    'available',
    '前端开发2年经验，熟悉现代前端框架',
    'active',
    NOW(),
    NOW()
);
```

### 2. 服务业零工

```sql
INSERT INTO worker_profile (
    user_id, real_name, nickname, gender, age, phone,
    current_location, work_locations, education_level, work_experience_years,
    work_categories, job_types_preferred, skills,
    salary_expectation_min, salary_expectation_max, salary_expectation_type,
    availability_status, self_introduction, status, create_time, update_time
) VALUES 
(
    4,
    '王五',
    '服务小王',
    'male',
    22,
    '13800138003',
    '青岛市市北区',
    '青岛市市北区,青岛市李沧区',
    '高中',
    1,
    '餐饮服务',
    '兼职,临时工',
    '服务意识,沟通能力,责任心,收银',
    18,
    30,
    '小时',
    'available',
    '有餐厅服务经验，态度认真负责',
    'active',
    NOW(),
    NOW()
),
(
    5,
    '赵六',
    '保洁赵',
    'female',
    35,
    '13800138004',
    '青岛市李沧区',
    '青岛市李沧区,青岛市城阳区',
    '初中',
    3,
    '保洁清洁',
    '临时工,小时工',
    '细心,勤劳,责任心',
    120,
    180,
    '日',
    'available',
    '3年保洁工作经验，工作细致认真',
    'active',
    NOW(),
    NOW()
);
```

## 匹配测试场景

### 场景1：完美匹配
- **招聘**: Java开发工程师 (青岛市市南区, 8000-15000元/月, Java技能)
- **零工**: 张三 (青岛市市南区, 7000-12000元/月期望, Java技能)
- **预期匹配度**: 90%+

### 场景2：地点不匹配
- **招聘**: 前端开发实习生 (青岛市崂山区)
- **零工**: 王五 (青岛市市北区, 不会前端技能)
- **预期匹配度**: 20%以下

### 场景3：技能部分匹配
- **招聘**: 前端开发实习生 (Vue.js, JavaScript)
- **零工**: 李四 (Vue.js, React, JavaScript)
- **预期匹配度**: 70-80%

## API测试示例

### 1. 测试招聘信息匹配零工

```bash
# 获取招聘信息ID为1的匹配零工
curl "http://localhost:80/sux-admin/public/job/postings/1/match-workers?limit=5"
```

### 2. 测试简化匹配算法

```bash
# 使用简化匹配算法
curl "http://localhost:80/sux-admin/public/job/simple/postings/1/match-workers?limit=5"
```

### 3. 快速匹配测试

```bash
# 按条件快速匹配
curl "http://localhost:80/sux-admin/public/job/quick-match?jobType=兼职&location=青岛&skills=JavaScript"
```

## 前端测试步骤

1. **访问人才特长页面**
   ```
   http://localhost/web-site/web-html/telent/talent/talentSpecial.html
   ```

2. **测试功能**
   - 点击"开启智能匹配"按钮
   - 点击招聘卡片上的"匹配零工"按钮
   - 使用筛选条件过滤招聘信息
   - 查看匹配度评分和匹配原因

## 数据优化建议

### 提高匹配效果的数据设置

1. **地点数据标准化**
   ```sql
   -- 统一使用标准地名格式
   '青岛市市南区' -- 而不是 '市南区' 或 '青岛市南'
   ```

2. **技能数据规范化**
   ```sql
   -- 使用逗号分隔的标准技能名称
   'Java,Spring Boot,MySQL' -- 而不是 'java spring mysql'
   ```

3. **薪资数据合理化**
   ```sql
   -- 确保薪资范围合理
   salary_min < salary_max
   -- 同类型工作薪资范围相近
   ```

4. **工作类型一致性**
   ```sql
   -- 使用统一的工作类型名称
   '全职', '兼职', '临时工', '小时工'
   ```

## 常见问题解决

### 1. 匹配度过低
- 检查地点数据格式是否一致
- 确认技能关键词是否匹配
- 验证薪资范围是否有重叠

### 2. 没有匹配结果
- 确保零工状态为 'active'
- 确保招聘信息状态为 'published'
- 检查数据库连接和API响应

### 3. 前端显示异常
- 检查浏览器控制台错误信息
- 验证API接口返回数据格式
- 确认JavaScript函数调用正确

通过以上数据和测试方法，您可以有效地测试招聘匹配系统的功能。
