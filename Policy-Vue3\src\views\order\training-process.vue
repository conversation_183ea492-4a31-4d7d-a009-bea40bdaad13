<template>
  <div class="training-process-container app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">培训订单流程管理</h1>
      <p class="page-subtitle">企业发布培训订单，培训机构线上认领并提交培训方案</p>
    </div>

    <!-- 流程导航 -->
    <div class="process-nav">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="企业发布流程" name="enterprise">
          <template #label>
            <span class="tab-label">
              <el-icon><OfficeBuilding /></el-icon>
              企业发布流程
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="机构认领流程" name="institution">
          <template #label>
            <span class="tab-label">
              <el-icon><School /></el-icon>
              机构认领流程
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 企业发布流程 -->
    <div v-show="activeTab === 'enterprise'" class="enterprise-process">
      <div class="process-section">
        <div class="section-header">
          <h2>企业发布培训订单流程</h2>
          <p>企业可以发布培训需求，线上招募优质培训机构</p>
        </div>

        <!-- 流程步骤 -->
        <div class="process-steps">
          <el-steps :active="enterpriseStep" finish-status="success" align-center>
            <el-step title="发布需求" description="企业发布培训订单需求"></el-step>
            <el-step title="机构申请" description="培训机构提交申请方案"></el-step>
            <el-step title="方案评审" description="企业评审培训方案"></el-step>
            <el-step title="确定合作" description="选定培训机构开始合作"></el-step>
          </el-steps>
        </div>

        <!-- 培训订单列表 -->
        <div class="order-list-section">
          <div class="section-title">
            <h3>培训订单列表</h3>
            <el-button type="primary" @click="handleCreateOrder">
              <el-icon><Plus /></el-icon>
              发布新订单
            </el-button>
          </div>

          <el-table :data="trainingOrders" stripe style="width: 100%">
            <el-table-column prop="orderTitle" label="订单标题" min-width="200">
              <template #default="{ row }">
                <el-link type="primary" @click="viewOrderDetail(row)">
                  {{ row.orderTitle }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="trainingType" label="培训类型" width="120"></el-table-column>
            <el-table-column prop="trainingLevel" label="培训级别" width="100"></el-table-column>
            <el-table-column prop="maxParticipants" label="招生人数" width="100"></el-table-column>
            <el-table-column prop="trainingFee" label="培训费用" width="120">
              <template #default="{ row }">
                <span v-if="row.trainingFee > 0">¥{{ row.trainingFee }}</span>
                <el-tag v-else type="success">免费</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="orderStatus" label="订单状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getOrderStatusType(row.orderStatus)">
                  {{ getOrderStatusText(row.orderStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="applicationCount" label="申请数量" width="100">
              <template #default="{ row }">
                <el-badge :value="row.applicationCount || 0" class="item">
                  <el-button size="small" @click="viewApplications(row)">查看申请</el-button>
                </el-badge>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewOrderDetail(row)">详情</el-button>
                <el-button size="small" type="primary" @click="viewApplications(row)">申请管理</el-button>
                <el-dropdown @command="(command) => handleOrderAction(command, row)">
                  <el-button size="small">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="publish" v-if="row.orderStatus === '0'">发布</el-dropdown-item>
                      <el-dropdown-item command="cancel" v-if="['1','2'].includes(row.orderStatus)">取消</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="enterpriseQuery.pageNum"
              v-model:page-size="enterpriseQuery.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="enterpriseTotal"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleEnterprisePageSizeChange"
              @current-change="handleEnterprisePageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 机构认领流程 -->
    <div v-show="activeTab === 'institution'" class="institution-process">
      <div class="process-section">
        <div class="section-header">
          <h2>培训机构认领流程</h2>
          <p>培训机构可以认领培训订单，提交培训计划和师资方案</p>
        </div>

        <!-- 流程步骤 -->
        <div class="process-steps">
          <el-steps :active="institutionStep" finish-status="success" align-center>
            <el-step title="浏览订单" description="查看可认领的培训订单"></el-step>
            <el-step title="提交申请" description="提交培训计划和师资方案"></el-step>
            <el-step title="等待审核" description="企业审核培训方案"></el-step>
            <el-step title="开始培训" description="审核通过后开始培训"></el-step>
          </el-steps>
        </div>

        <!-- 可认领订单列表 -->
        <div class="available-orders-section">
          <div class="section-title">
            <h3>可认领培训订单</h3>
            <div class="filter-controls">
              <el-input
                v-model="institutionQuery.keyword"
                placeholder="搜索订单标题或培训类型"
                style="width: 300px; margin-right: 10px;"
                @keyup.enter="searchAvailableOrders"
              >
                <template #append>
                  <el-button @click="searchAvailableOrders">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
              <el-select v-model="institutionQuery.trainingType" placeholder="培训类型" style="width: 150px; margin-right: 10px;" @change="searchAvailableOrders">
                <el-option label="全部" value=""></el-option>
                <el-option label="技术培训" value="技术培训"></el-option>
                <el-option label="管理培训" value="管理培训"></el-option>
                <el-option label="职业技能" value="职业技能"></el-option>
              </el-select>
              <el-select v-model="institutionQuery.trainingLevel" placeholder="培训级别" style="width: 120px;" @change="searchAvailableOrders">
                <el-option label="全部" value=""></el-option>
                <el-option label="初级" value="初级"></el-option>
                <el-option label="中级" value="中级"></el-option>
                <el-option label="高级" value="高级"></el-option>
              </el-select>
            </div>
          </div>

          <div class="orders-grid">
            <div v-for="order in availableOrders" :key="order.orderId" class="order-card">
              <div class="card-header">
                <h4 class="order-title">{{ order.orderTitle }}</h4>
                <el-tag :type="getOrderStatusType(order.orderStatus)" size="small">
                  {{ getOrderStatusText(order.orderStatus) }}
                </el-tag>
              </div>
              <div class="card-content">
                <div class="order-info">
                  <div class="info-item">
                    <span class="label">培训类型：</span>
                    <span class="value">{{ order.trainingType }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">培训级别：</span>
                    <span class="value">{{ order.trainingLevel }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">招生人数：</span>
                    <span class="value">{{ order.maxParticipants }}人</span>
                  </div>
                  <div class="info-item">
                    <span class="label">培训费用：</span>
                    <span class="value">
                      <span v-if="order.trainingFee > 0">¥{{ order.trainingFee }}</span>
                      <el-tag v-else type="success" size="small">免费</el-tag>
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="label">报名截止：</span>
                    <span class="value">{{ formatDate(order.registrationDeadline) }}</span>
                  </div>
                </div>
                <div class="order-description">
                  <p>{{ order.orderDescription }}</p>
                </div>
              </div>
              <div class="card-footer">
                <el-button size="small" @click="viewOrderDetail(order)">查看详情</el-button>
                <el-button size="small" type="primary" @click="applyForOrder(order)">
                  <el-icon><Document /></el-icon>
                  申请认领
                </el-button>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="institutionQuery.pageNum"
              v-model:page-size="institutionQuery.pageSize"
              :page-sizes="[12, 24, 48]"
              :total="institutionTotal"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleInstitutionPageSizeChange"
              @current-change="handleInstitutionPageChange"
            />
          </div>
        </div>

        <!-- 我的申请列表 -->
        <div class="my-applications-section">
          <div class="section-title">
            <h3>我的申请记录</h3>
          </div>

          <el-table :data="myApplications" stripe style="width: 100%">
            <el-table-column prop="orderTitle" label="培训订单" min-width="200"></el-table-column>
            <el-table-column prop="trainingType" label="培训类型" width="120"></el-table-column>
            <el-table-column prop="applicationTime" label="申请时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.applicationTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="applicationStatus" label="申请状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getApplicationStatusType(row.applicationStatus)">
                  {{ getApplicationStatusText(row.applicationStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reviewTime" label="审核时间" width="180">
              <template #default="{ row }">
                {{ row.reviewTime ? formatDateTime(row.reviewTime) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="viewApplicationDetail(row)">查看详情</el-button>
                <el-button size="small" type="primary" @click="editApplication(row)" v-if="row.applicationStatus === '0'">编辑申请</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="applicationQuery.pageNum"
              v-model:page-size="applicationQuery.pageSize"
              :page-sizes="[10, 20, 50]"
              :total="applicationTotal"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleApplicationPageSizeChange"
              @current-change="handleApplicationPageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 培训机构申请弹窗 -->
    <InstitutionApplicationDialog
      ref="applicationDialogRef"
      @submit="handleApplicationSubmit"
      @cancel="handleApplicationCancel"
    />

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog
      ref="orderDetailDialogRef"
    />

    <!-- 申请管理弹窗 -->
    <ApplicationManagementDialog
      ref="applicationManagementDialogRef"
      @approve="handleApplicationApprove"
      @reject="handleApplicationReject"
    />
  </div>
</template>

<script setup name="TrainingProcess">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import {
  OfficeBuilding,
  School,
  Plus,
  Search,
  Document,
  ArrowDown
} from '@element-plus/icons-vue'
import { listTrainingOrder, publishTrainingOrder, cancelTrainingOrder } from "@/api/training/order"
import InstitutionApplicationDialog from './components/InstitutionApplicationDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'
import ApplicationManagementDialog from './components/ApplicationManagementDialog.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const activeTab = ref('enterprise')
const enterpriseStep = ref(1)
const institutionStep = ref(1)

// 企业发布流程数据
const trainingOrders = ref([])
const enterpriseTotal = ref(0)
const enterpriseQuery = reactive({
  pageNum: 1,
  pageSize: 10,
  orderStatus: undefined
})

// 机构认领流程数据
const availableOrders = ref([])
const institutionTotal = ref(0)
const institutionQuery = reactive({
  pageNum: 1,
  pageSize: 12,
  keyword: '',
  trainingType: '',
  trainingLevel: '',
  orderStatus: '1' // 只显示已发布的订单
})

// 我的申请数据
const myApplications = ref([])
const applicationTotal = ref(0)
const applicationQuery = reactive({
  pageNum: 1,
  pageSize: 10
})

// 弹窗引用
const applicationDialogRef = ref(null)
const orderDetailDialogRef = ref(null)
const applicationManagementDialogRef = ref(null)

// 初始化
onMounted(() => {
  loadTrainingOrders()
  loadAvailableOrders()
  loadMyApplications()
})

// 加载培训订单列表（企业视角）
const loadTrainingOrders = async () => {
  try {
    const response = await listTrainingOrder(enterpriseQuery)
    trainingOrders.value = response.rows || []
    enterpriseTotal.value = response.total || 0
  } catch (error) {
    console.error('加载培训订单失败:', error)
    proxy.$modal.msgError('加载培训订单失败')
  }
}

// 加载可认领订单列表（机构视角）
const loadAvailableOrders = async () => {
  try {
    const response = await listTrainingOrder(institutionQuery)
    availableOrders.value = response.rows || []
    institutionTotal.value = response.total || 0
  } catch (error) {
    console.error('加载可认领订单失败:', error)
    proxy.$modal.msgError('加载可认领订单失败')
  }
}

// 加载我的申请记录
const loadMyApplications = async () => {
  try {
    // 这里需要调用培训机构申请相关的API
    // const response = await listMyTrainingApplications(applicationQuery)
    // myApplications.value = response.rows || []
    // applicationTotal.value = response.total || 0

    // 临时模拟数据
    myApplications.value = []
    applicationTotal.value = 0
  } catch (error) {
    console.error('加载申请记录失败:', error)
    proxy.$modal.msgError('加载申请记录失败')
  }
}

// 标签切换处理
const handleTabClick = (tab) => {
  if (tab.name === 'institution') {
    loadAvailableOrders()
    loadMyApplications()
  } else {
    loadTrainingOrders()
  }
}

// 企业流程 - 创建订单
const handleCreateOrder = () => {
  // 跳转到订单创建页面或打开创建弹窗
  proxy.$router.push('/order/index')
}

// 企业流程 - 查看订单详情
const viewOrderDetail = (order) => {
  orderDetailDialogRef.value?.openDialog(order)
}

// 企业流程 - 查看申请列表
const viewApplications = (order) => {
  applicationManagementDialogRef.value?.openDialog(order)
}

// 企业流程 - 订单操作
const handleOrderAction = async (command, order) => {
  try {
    switch (command) {
      case 'edit':
        // 跳转到编辑页面
        proxy.$router.push(`/order/edit/${order.orderId}`)
        break
      case 'publish':
        await proxy.$modal.confirm(`确认要发布"${order.orderTitle}"培训订单吗？`)
        await publishTrainingOrder(order.orderId)
        proxy.$modal.msgSuccess('发布成功')
        loadTrainingOrders()
        break
      case 'cancel':
        await proxy.$modal.confirm(`确认要取消"${order.orderTitle}"培训订单吗？`)
        await cancelTrainingOrder(order.orderId)
        proxy.$modal.msgSuccess('取消成功')
        loadTrainingOrders()
        break
    }
  } catch (error) {
    console.error('订单操作失败:', error)
    proxy.$modal.msgError('操作失败')
  }
}

// 机构流程 - 搜索可认领订单
const searchAvailableOrders = () => {
  institutionQuery.pageNum = 1
  loadAvailableOrders()
}

// 机构流程 - 申请认领订单
const applyForOrder = (order) => {
  applicationDialogRef.value?.openDialog(order)
}

// 机构流程 - 查看申请详情
const viewApplicationDetail = (application) => {
  // 打开申请详情弹窗
  console.log('查看申请详情:', application)
}

// 机构流程 - 编辑申请
const editApplication = (application) => {
  // 打开编辑申请弹窗
  console.log('编辑申请:', application)
}

// 处理申请提交
const handleApplicationSubmit = async (applicationData) => {
  try {
    // 调用提交申请的API
    // await submitTrainingApplication(applicationData)
    proxy.$modal.msgSuccess('申请提交成功')
    loadMyApplications()
  } catch (error) {
    console.error('申请提交失败:', error)
    proxy.$modal.msgError('申请提交失败')
  }
}

// 处理申请取消
const handleApplicationCancel = () => {
  // 取消申请操作
}

// 处理申请审批
const handleApplicationApprove = async (applicationId) => {
  try {
    // 调用审批通过API
    // await approveTrainingApplication(applicationId)
    proxy.$modal.msgSuccess('审批通过')
    loadTrainingOrders()
  } catch (error) {
    console.error('审批失败:', error)
    proxy.$modal.msgError('审批失败')
  }
}

// 处理申请拒绝
const handleApplicationReject = async (applicationId, reason) => {
  try {
    // 调用审批拒绝API
    // await rejectTrainingApplication(applicationId, reason)
    proxy.$modal.msgSuccess('已拒绝申请')
    loadTrainingOrders()
  } catch (error) {
    console.error('操作失败:', error)
    proxy.$modal.msgError('操作失败')
  }
}

// 分页处理方法
const handleEnterprisePageSizeChange = (size) => {
  enterpriseQuery.pageSize = size
  enterpriseQuery.pageNum = 1
  loadTrainingOrders()
}

const handleEnterprisePageChange = (page) => {
  enterpriseQuery.pageNum = page
  loadTrainingOrders()
}

const handleInstitutionPageSizeChange = (size) => {
  institutionQuery.pageSize = size
  institutionQuery.pageNum = 1
  loadAvailableOrders()
}

const handleInstitutionPageChange = (page) => {
  institutionQuery.pageNum = page
  loadAvailableOrders()
}

const handleApplicationPageSizeChange = (size) => {
  applicationQuery.pageSize = size
  applicationQuery.pageNum = 1
  loadMyApplications()
}

const handleApplicationPageChange = (page) => {
  applicationQuery.pageNum = page
  loadMyApplications()
}

// 状态处理方法
const getOrderStatusType = (status) => {
  const statusMap = {
    '0': 'info',     // 草稿
    '1': 'success',  // 发布
    '2': 'warning',  // 进行中
    '3': 'primary',  // 已完成
    '4': 'danger'    // 已取消
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    '0': '草稿',
    '1': '发布',
    '2': '进行中',
    '3': '已完成',
    '4': '已取消'
  }
  return statusMap[status] || '未知'
}

const getApplicationStatusType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待审核
    '1': 'success',  // 审核通过
    '2': 'danger',   // 审核拒绝
    '3': 'info'      // 已取消
  }
  return statusMap[status] || 'info'
}

const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '审核通过',
    '2': '审核拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

// 日期格式化方法
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style lang="scss" scoped>
.training-process-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 32px;
    font-weight: 600;
    margin: 0 0 10px 0;
  }

  .page-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }
}

.process-nav {
  margin-bottom: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
  }

  .tab-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
  }
}

.process-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  text-align: center;
  margin-bottom: 40px;

  h2 {
    font-size: 24px;
    color: #303133;
    margin: 0 0 10px 0;
  }

  p {
    color: #606266;
    font-size: 14px;
    margin: 0;
  }
}

.process-steps {
  margin-bottom: 40px;
  padding: 20px 0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f2f5;

  h3 {
    font-size: 18px;
    color: #303133;
    margin: 0;
  }
}

.filter-controls {
  display: flex;
  align-items: center;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.order-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #409eff;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;

  .order-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    flex: 1;
    margin-right: 10px;
    line-height: 1.4;
  }
}

.card-content {
  margin-bottom: 20px;
}

.order-info {
  margin-bottom: 15px;

  .info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    .label {
      color: #909399;
      width: 80px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;
    }
  }
}

.order-description {
  p {
    color: #606266;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.card-footer {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

// 表格样式优化
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .el-table__header {
    background-color: #f8f9fa;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .training-process-container {
    padding: 10px;
  }

  .page-header {
    padding: 20px 15px;

    .page-title {
      font-size: 24px;
    }

    .page-subtitle {
      font-size: 14px;
    }
  }

  .process-section {
    padding: 20px 15px;
  }

  .orders-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .section-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .filter-controls {
    flex-direction: column;
    width: 100%;
    gap: 10px;

    .el-input,
    .el-select {
      width: 100% !important;
    }
  }
}
</style>
