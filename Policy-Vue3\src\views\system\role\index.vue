<template>
  <div class="role-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="tableData" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="280"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['system:role:add']">新 增</el-button>
        <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['system:role:export']">导
          出</el-button>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row, index, $index }">
        <el-switch
          :model-value="row?.status || (tableData[index] && tableData[index].status) || (tableData[$index] && tableData[$index].status)"
          active-value="0" inactive-value="1"
          @update:model-value="(val) => handleStatusChangeByIndex(val, row, index, $index)"></el-switch>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)" v-hasPermi="['system:role:query']">
            查看
          </el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['system:role:edit']"
            v-if="row.roleId !== 1">
            修改
          </el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['system:role:remove']"
            v-if="row.roleId !== 1">
            删除
          </el-button>
          <el-button type="warning" link @click="handleDataScope(row)" v-hasPermi="['system:role:edit']"
            v-if="row.roleId !== 1">
            数据权限
          </el-button>
          <el-button type="success" link @click="handleAuthUser(row)" v-hasPermi="['system:role:edit']"
            v-if="row.roleId !== 1">
            分配用户
          </el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <RoleFormDialog ref="roleFormDialogRef" :formFields="formFields" :formOption="formOption" @submit="handleFormSubmit"
      @cancel="handleFormCancel" />

    <!-- 数据权限对话框组件 -->
    <DataScopeDialog ref="dataScopeDialogRef" :dataScopeOptions="dataScopeOptions" @submit="handleDataScopeSubmit"
      @cancel="handleDataScopeCancel" />
  </div>
</template>

<script setup name="Role">
import { ref, onMounted, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { createRoleTableOption } from "@/const/system/role";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import TableList from '@/components/TableList/index.vue';
import RoleFormDialog from './RoleFormDialog.vue';
import DataScopeDialog from './DataScopeDialog.vue';
import { addRole, changeRoleStatus, dataScope, delRole, getRole, listRole, updateRole } from "@/api/system/role"

const router = useRouter();
const { proxy } = getCurrentInstance();

const tableColumns = ref([]);
const searchableColumns = ref([]);
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
  dialogWidth: '600px',
  dialogHeight: '70vh'
});
const tableListRef = ref(null);
const roleFormDialogRef = ref(null);
const dataScopeDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 数据权限相关
const dataScopeOptions = ref([]);

// 选择相关
const selectedRows = ref([]);
const single = ref(true);
const multiple = ref(true);

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createRoleTableOption(proxy);

    if (!baseOption) {
      throw new Error('无法获取基础配置');
    }

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    // 保存数据权限选项
    dataScopeOptions.value = baseOption.dataScopeOptions || [];

    isTableReady.value = true;

    // 加载表格数据
    loadData();
  } catch (error) {
    console.error('初始化配置失败:', error);
    isTableReady.value = false;
    proxy?.$modal?.msgError('页面初始化失败: ' + error.message);
  }
};

// 加载表格数据
const loadData = () => {
  tableLoading.value = true;

  // 构建查询参数
  const queryParams = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    ...searchParams.value
  };

  listRole(queryParams).then(response => {
    if (response && response.rows) {
      tableData.value = response.rows;
      total.value = response.total || 0;
    } else {
      tableData.value = [];
      total.value = 0;
    }

    tableLoading.value = false;
  }).catch(error => {
    console.error('加载数据失败:', error);
    tableLoading.value = false;
    proxy?.$modal?.msgError('数据加载失败');
  });
};

// 处理搜索
const handleSearch = (searchData) => {
  searchParams.value = searchData || {};
  currentPage.value = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchParams.value = {};
  currentPage.value = 1;
  loadData();
};

// 处理分页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadData();
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadData();
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection || [];
  single.value = selection?.length !== 1;
  multiple.value = !selection?.length;
};

// 新增
const handleAdd = () => {
  roleFormDialogRef.value?.openDialog('add', '新增角色');
};

// 查看
const handleView = (row) => {
  roleFormDialogRef.value?.openDialog('view', '查看角色', row);
};

// 编辑
const handleUpdate = (row) => {
  const roleId = row ? row.roleId : selectedRows.value[0]?.roleId;
  if (!roleId) return;

  getRole(roleId).then(response => {
    const roleData = response.data;
    roleData.roleSort = Number(roleData.roleSort);
    roleFormDialogRef.value?.openDialog('edit', '修改角色', roleData);
  }).catch(error => {
    console.error('获取角色详情失败:', error);
    proxy?.$modal?.msgError('获取角色详情失败');
  });
};

// 删除
const handleDelete = (row) => {
  const roleIds = row ? [row.roleId] : selectedRows.value.map(item => item.roleId);
  const roleNames = row ? [row.roleName] : selectedRows.value.map(item => item.roleName);

  if (!roleIds.length) {
    proxy?.$modal?.msgError('请选择要删除的角色');
    return;
  }

  proxy.$modal.confirm(`是否确认删除角色"${roleNames.join('、')}"？`).then(() => {
    return delRole(roleIds.join(','));
  }).then(() => {
    loadData();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(error => {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      proxy?.$modal?.msgError('删除失败');
    }
  });
};

// 导出
const handleExport = () => {
  try {
    proxy.download("system/role/export", {
      ...searchParams.value,
    }, `role_${new Date().getTime()}.xlsx`);
  } catch (error) {
    console.error('导出失败:', error);
    proxy?.$modal?.msgError('导出失败');
  }
};

// 通过索引处理状态变更
const handleStatusChangeByIndex = (newStatus, row, index, $index) => {
  // 尝试从多个来源获取正确的行数据
  let targetRow = null;

  if (row && row.roleId) {
    targetRow = row;
  } else if (index !== undefined && tableData.value[index]) {
    targetRow = tableData.value[index];
  } else if ($index !== undefined && tableData.value[$index]) {
    targetRow = tableData.value[$index];
  } else {
    console.error('无法获取有效的行数据');
    return;
  }

  // 更新状态
  targetRow.status = newStatus;

  // 调用状态切换处理
  handleStatusChange(targetRow);
};

// 角色状态修改
const handleStatusChange = (row) => {
  if (!row || typeof row !== 'object' || !row.roleId) {
    console.error('状态切换失败：行数据无效', row);
    return;
  }

  let text = row.status === "0" ? "启用" : "停用";
  let roleName = row.roleName || row.name || "该";

  proxy.$modal.confirm('确认要"' + text + '""' + roleName + '"角色吗?').then(() => {
    return changeRoleStatus(row.roleId, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
    loadData();
  }).catch((error) => {
    if (error !== 'cancel') {
      // 恢复原状态
      row.status = row.status === "0" ? "1" : "0";
      console.error('状态切换失败:', error);
    }
  });
};

// 移除了 handleCommand 函数，因为现在直接使用按钮调用对应函数

// 分配数据权限
const handleDataScope = (row) => {
  dataScopeDialogRef.value?.openDialog(row);
};

// 分配用户
const handleAuthUser = (row) => {
  router.push("/system/role-auth/user/" + row.roleId);
};

// 处理数据权限提交
const handleDataScopeSubmit = (formData) => {
  dataScope(formData).then(response => {
    proxy.$modal.msgSuccess("修改成功");
    loadData();
  }).catch(error => {
    console.error('数据权限修改失败:', error);
    proxy?.$modal?.msgError('数据权限修改失败');
  });
};

// 处理数据权限取消
const handleDataScopeCancel = () => {
  // 数据权限取消逻辑已在对话框组件中处理
};

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    if (formData.type === 'add') {
      await addRole(formData.data);
      proxy.$modal.msgSuccess("新增成功");
    } else if (formData.type === 'edit') {
      await updateRole(formData.data);
      proxy.$modal.msgSuccess("修改成功");
    }

    roleFormDialogRef.value?.onSubmitSuccess();
    loadData();
  } catch (error) {
    console.error('表单提交失败:', error);
    roleFormDialogRef.value?.onSubmitError();
  }
};

// 处理表单取消
const handleFormCancel = () => {
  // 表单取消逻辑已在对话框组件中处理
};

// 初始化时获取数据
onMounted(async () => {
  await initializeConfig();
});
</script>

<style lang="scss" scoped>
.role-container {

  .operation-btns {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: wrap;

    .op-btn {
      padding: 4px 8px;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .loading-placeholder {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
