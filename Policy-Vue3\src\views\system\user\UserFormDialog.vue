<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <FormList ref="formListRef" v-model="formData" :fields="currentFormFields" :is-view="dialogType === 'view'"
                :showActions="false" :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange" v-if="dialogType !== 'view'">

                <!-- 部门选择插槽 -->
                <template #deptName="{ row }">
                    <el-tree-select v-model="formData.deptId" :data="deptOptions"
                        :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id"
                        placeholder="请选择归属部门" check-strictly style="width: 100%" />
                </template>

                <!-- 状态单选按钮组插槽 -->
                <template #status="{ row }">
                    <el-radio-group v-model="formData.status">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">
                            {{ dict.label }}
                        </el-radio>
                    </el-radio-group>
                </template>

                <!-- 岗位选择插槽 -->
                <template #postIds="{ row }" v-if="formData.userType === '00'">
                    <el-select v-model="formData.postIds" multiple placeholder="请选择岗位" style="width: 100%">
                        <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName"
                            :value="item.postId" :disabled="item.status == 1">
                        </el-option>
                    </el-select>
                </template>

                <!-- 角色选择插槽 -->
                <template #roleIds="{ row }" v-if="formData.userType === '00'">
                    <el-select v-model="formData.roleIds" multiple placeholder="请选择角色" style="width: 100%">
                        <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName"
                            :value="item.roleId" :disabled="item.status == 1">
                        </el-option>
                    </el-select>
                </template>
            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="currentFormFields" :labelWidth="formOption.labelWidth">
                <!-- 部门查看插槽 -->
                <template #deptName="{ row }">
                    <span>{{ formData.dept?.deptName || '-' }}</span>
                </template>

                <!-- 状态查看插槽 -->
                <template #status="{ row }">
                    <el-tag :type="formData.status === '0' ? 'success' : 'danger'">
                        {{ getDictLabel(sys_normal_disable, formData.status) }}
                    </el-tag>
                </template>
            </ViewList>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="custom-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退出全屏' : '全屏显示' }}
                </el-button>
                <el-button class="custom-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关 闭' : '取 消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="custom-btn" @click="handleSubmitForm"
                    :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="UserFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import FormList from '@/components/FormList/index.vue';
import ViewList from '@/components/ViewList/index.vue';

const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex");

// Props
const props = defineProps({
    formFields: {
        type: Array,
        default: () => []
    },
    formOption: {
        type: Object,
        default: () => ({
            dialogWidth: '900px',
            dialogHeight: '70vh'
        })
    },
    deptOptions: {
        type: Array,
        default: () => []
    },
    postOptions: {
        type: Array,
        default: () => []
    },
    roleOptions: {
        type: Array,
        default: () => []
    }
});

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('add'); // add, edit, view
const dialogTitle = ref('新增用户');
const formListRef = ref(null);
const formData = ref({});
const submitLoading = ref(false);
const submitDisabled = ref(false);

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 弹窗宽度计算
const dialogWidth = computed(() => {
    if (isFullscreen.value) {
        return '100%';
    }
    return props.formOption.dialogWidth || '900px';
});

// 根据表单类型动态筛选字段
const currentFormFields = computed(() => {
    if (!props.formFields.length) return [];

    let filteredFields = [];

    if (dialogType.value === 'add') {
        filteredFields = props.formFields.filter(field => field.addDisplay !== false);
    }
    else if (dialogType.value === 'edit') {
        filteredFields = props.formFields.filter(field => field.editDisplay !== false);
    }
    else { // view模式
        filteredFields = props.formFields.filter(field => field.viewDisplay !== false);
    }

    // 根据用户类型进一步过滤字段
    return filteredFields.filter(field => {
        // 如果字段有display函数，则调用该函数判断是否显示
        if (typeof field.display === 'function') {
            return field.display(formData.value);
        }
        return true;
    });
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    const baseStyle = {
        overflow: 'visible',
        padding: '20px 10px',
        overflowX: 'hidden',
    };

    if (isFullscreen.value) {
        return {
            ...baseStyle,
            maxHeight: 'calc(100vh - 180px)',
            overflowY: 'auto',
            overflowX: 'hidden',
        };
    }

    return {
        ...baseStyle,
        maxHeight: props.formOption.dialogHeight || '70vh',
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: 'auto',
    };
});

// 切换全屏状态
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
};

// 获取字典标签
const getDictLabel = (dictData, value) => {
    const dict = dictData.find(item => item.value === value);
    return dict ? dict.label : value;
};

// 对外暴露的方法
const openDialog = (type, title, data = {}) => {
    dialogType.value = type;
    dialogTitle.value = title;

    // 初始化表单数据
    if (type === 'add') {
        formData.value = {
            userId: undefined,
            deptId: undefined,
            userName: undefined,
            nickName: undefined,
            password: undefined,
            phonenumber: undefined,
            email: undefined,
            sex: undefined,
            userType: "00", // 默认为系统用户
            status: "0",
            remark: undefined,
            postIds: [],
            roleIds: []
        };
    } else {
        formData.value = { ...data };
        // 确保编辑时也有userType字段
        if (!formData.value.userType) {
            formData.value.userType = "00";
        }
    }

    dialogVisible.value = true;
    isFullscreen.value = false; // 重置全屏状态
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 处理表单字段变更
const handleFieldChange = (field, value) => {
    // 当用户类型改变时，清空岗位和角色选择
    if (field === 'userType') {
        if (value === '01') { // 普通用户
            formData.value.postIds = [];
            formData.value.roleIds = [];
        }
    }
};

// 处理表单提交
const handleSubmitForm = async () => {
    // 防止重复提交
    if (submitLoading.value || submitDisabled.value) {
        return;
    }

    // 确保formListRef存在
    if (!formListRef.value) {
        return;
    }

    try {
        // 设置按钮状态
        submitLoading.value = true;
        submitDisabled.value = true;

        // 表单验证
        await formListRef.value.validate();

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        });

    } catch (error) {
        // 验证失败不显示错误消息，因为表单会自动显示错误
        submitLoading.value = false;
        submitDisabled.value = false;
    }
};

// 处理取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
    // 设置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
    // 清空表单数据
    formData.value = {};

    // 重置提交状态
    submitLoading.value = false;
    submitDisabled.value = false;
    isFullscreen.value = false;
};

// 提交成功后的回调
const onSubmitSuccess = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
    closeDialog();
};

// 提交失败后的回调
const onSubmitError = () => {
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 暴露给父组件的方法
defineExpose({
    openDialog,
    closeDialog,
    onSubmitSuccess,
    onSubmitError
});
</script>

<style lang="scss" scoped>
// 弹窗底部样式优化
:deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f2f5;
    background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 0 0 12px 12px;

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;
    }
}

// 弹窗主体样式优化
:deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .el-dialog__header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;
        background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
        border-radius: 12px 12px 0 0;
        overflow: hidden;

        .el-dialog__title {
            color: #303133;
            font-weight: 600;
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .el-dialog__body {
        padding: 0;
        background: #ffffff;
        overflow-x: hidden !important;
        width: 100%;
        box-sizing: border-box;
    }
}
</style>