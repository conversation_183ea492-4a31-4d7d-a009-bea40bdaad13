# 政策申请管理页面 - 更新说明

## 修改内容

根据您的要求，已对政策申请管理页面进行了以下修改：

### 1. 数据查询方式调整

**修改前：**
- 根据用户权限调用不同的API接口
- 初审员只能看到待初审的申请
- 终审员只能看到待终审的申请
- 管理员看到所有申请

**修改后：**
- 统一使用 `listPolicyApplication` 接口查询所有数据
- 所有用户都能看到完整的政策申请列表
- 通过权限控制显示不同的操作按钮

### 2. 操作按钮调整

**修改前：**
- 初审按钮
- 终审按钮

**修改后：**
- 一级审核按钮
- 二级审核按钮

### 3. 按钮显示逻辑

#### 一级审核按钮
- **显示条件**: 用户有一级审核权限 (`policy:application:first-review`) 且申请状态为"待初审"(0)
- **按钮文本**: "一级审核"
- **按钮样式**: `type="warning"`

#### 二级审核按钮
- **显示条件**: 用户有二级审核权限 (`policy:application:final-review`) 且申请状态为"待终审"(3)
- **按钮文本**: "二级审核"
- **按钮样式**: `type="danger"`

#### 其他按钮
- **查看**: 所有用户都可以查看申请详情
- **审核记录**: 所有用户都可以查看审核记录
- **编辑/删除**: 只有管理员权限用户可见

### 4. 审核弹窗调整

**修改内容：**
- 弹窗标题从"初审/终审"改为"一级审核/二级审核"
- 确认按钮文本从"确认初审/确认终审"改为"确认一级审核/确认二级审核"
- 成功提示从"初审完成/终审完成"改为"一级审核完成/二级审核完成"

### 5. 代码优化

**清理内容：**
- 移除了不再使用的API导入：`listPendingFirstReview`、`listPendingFinalReview`、`listAllApplications`
- 移除了未使用的变量：`loading`
- 简化了数据查询逻辑

## 当前功能特点

### 1. 统一数据展示
- 所有用户看到相同的申请列表
- 完整的申请状态信息展示
- 统一的搜索和分页功能

### 2. 权限控制的操作按钮
- 根据用户权限和申请状态动态显示操作按钮
- 一级审核员只能对待初审的申请进行一级审核
- 二级审核员只能对待终审的申请进行二级审核
- 管理员具有完整的管理权限

### 3. 审核流程
```
待初审(0) → [一级审核] → 待终审(3) → [二级审核] → 已完成(6)
     ↓                        ↓
   初审拒绝(2)              终审拒绝(5)
```

### 4. 状态标签显示
- 待初审: 橙色标签
- 初审通过: 绿色标签
- 初审拒绝: 红色标签
- 待终审: 橙色标签
- 终审通过: 绿色标签
- 终审拒绝: 红色标签
- 已完成: 蓝色标签

## API接口使用

### 当前使用的接口
- `GET /policy/application/list` - 查询所有政策申请列表
- `GET /policy/application/{id}` - 查询申请详情
- `POST /policy/application/first-review` - 一级审核操作
- `POST /policy/application/final-review` - 二级审核操作
- `GET /policy/application/approval-records/{id}` - 查询审核记录

### 权限要求
- `policy:application:list` - 查看申请列表
- `policy:application:query` - 查看申请详情
- `policy:application:first-review` - 一级审核权限
- `policy:application:final-review` - 二级审核权限
- `policy:application:admin` - 管理员权限

## 使用说明

### 一级审核员
1. 登录系统后可以看到所有申请列表
2. 对于状态为"待初审"的申请，会显示"一级审核"按钮
3. 点击"一级审核"按钮进行审核操作
4. 审核通过后，申请状态自动变为"待终审"

### 二级审核员
1. 登录系统后可以看到所有申请列表
2. 对于状态为"待终审"的申请，会显示"二级审核"按钮
3. 点击"二级审核"按钮进行最终审核
4. 审核通过后，申请状态自动变为"已完成"

### 管理员
1. 可以看到所有申请列表
2. 具有编辑和删除申请的权限
3. 可以查看所有审核记录

### 普通用户
1. 可以查看所有申请列表
2. 可以查看申请详情和审核记录
3. 无审核操作权限

## 注意事项

1. **权限控制**: 系统严格按照权限控制按钮显示，确保用户只能执行有权限的操作
2. **状态流转**: 审核状态按照预定义流程自动流转，不可逆转
3. **数据完整性**: 所有用户都能看到完整的申请列表，提高信息透明度
4. **操作日志**: 所有审核操作都会记录在审核记录表中，便于追溯

这样的设计既保证了数据的完整展示，又通过权限控制确保了操作的安全性。
