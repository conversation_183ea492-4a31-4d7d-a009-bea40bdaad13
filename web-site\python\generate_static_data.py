import json
import os
from pathlib import Path

def generate_mock_data():
    """生成模拟数据"""
    
    # 地域数据
    regions_data = {
        "code": 0,
        "obj": {
            "place": {"baseName": "中央", "baseId": "central"},
            "province": {"baseName": "山东省", "baseId": "shandong"},
            "city": {"baseName": "青岛市", "baseId": "qingdao"},
            "district": [
                {"baseName": "市南区", "baseId": "shinan"},
                {"baseName": "市北区", "baseId": "shibei"},
                {"baseName": "李沧区", "baseId": "licang"},
                {"baseName": "崂山区", "baseId": "laoshan"},
                {"baseName": "城阳区", "baseId": "chengyang"},
                {"baseName": "即墨区", "baseId": "jimo"},
                {"baseName": "胶州市", "baseId": "jiaozhou"},
                {"baseName": "平度市", "baseId": "pingdu"},
                {"baseName": "莱西市", "baseId": "laixi"},
                {"baseName": "西海岸新区", "baseId": "xihaian"}
            ]
        }
    }
    
    # 政策类型数据
    policy_types_data = {
        "code": 0,
        "obj": [
            {"baseName": "创业补贴", "baseId": "cybt"},
            {"baseName": "创业贷款", "baseId": "cydk"},
            {"baseName": "税收优惠", "baseId": "ssyh"},
            {"baseName": "场地支持", "baseId": "cdzc"},
            {"baseName": "培训补贴", "baseId": "pxbt"},
            {"baseName": "社保补贴", "baseId": "sbbt"},
            {"baseName": "人才引进", "baseId": "rcyj"},
            {"baseName": "科技创新", "baseId": "kjcx"}
        ]
    }
    
    # 政策支持方式数据
    support_modes_data = {
        "code": 0,
        "obj": [
            {"baseName": "资金补贴", "baseId": "zjbt"},
            {"baseName": "贷款贴息", "baseId": "dktx"},
            {"baseName": "税收减免", "baseId": "ssjm"},
            {"baseName": "场地租金减免", "baseId": "cdzjjm"},
            {"baseName": "免费培训", "baseId": "mfpx"},
            {"baseName": "专家指导", "baseId": "zjzd"},
            {"baseName": "优先审批", "baseId": "yxsp"}
        ]
    }
    
    # 政策文件列表
    file_list_data = {
        "code": 0,
        "obj": {
            "content": [
                {
                    "baseId": "file001",
                    "title": "青岛市创业补贴申请办法",
                    "policyTypeName": "创业补贴",
                    "supportmodeName": "资金补贴",
                    "publishTime": "2024-01-15"
                },
                {
                    "baseId": "file002", 
                    "title": "关于进一步支持大学生创业的若干措施",
                    "policyTypeName": "创业贷款",
                    "supportmodeName": "贷款贴息",
                    "publishTime": "2024-01-10"
                },
                {
                    "baseId": "file003",
                    "title": "青岛市小微企业税收优惠政策实施细则",
                    "policyTypeName": "税收优惠", 
                    "supportmodeName": "税收减免",
                    "publishTime": "2024-01-08"
                }
            ]
        }
    }
    
    # 政策申报列表
    calendar_list_data = {
        "code": 0,
        "obj": {
            "content": [
                {
                    "baseId": "cal001",
                    "sname": "2024年青岛市创业补贴申报",
                    "competentDepartment": "青岛市人力资源和社会保障局",
                    "applytime": "2024-01-01 至 2024-12-31",
                    "projectTypeName": "创业补贴",
                    "areaName": "青岛市",
                    "rewardMoney": "10",
                    "statusText": "1",
                    "detailStatusName": "申请中",
                    "applyUrl": "javascript:;"
                },
                {
                    "baseId": "cal002",
                    "sname": "大学生创业担保贷款申请",
                    "competentDepartment": "青岛市财政局",
                    "applytime": "2024-02-01 至 2024-11-30",
                    "projectTypeName": "创业贷款",
                    "areaName": "青岛市",
                    "methodName": "贷款贴息",
                    "statusText": "1",
                    "detailStatusName": "申请中",
                    "applyUrl": "javascript:;"
                },
                {
                    "baseId": "cal003",
                    "sname": "小微企业税收减免申请",
                    "competentDepartment": "青岛市税务局",
                    "applytime": "2024-03-01 至 2024-10-31",
                    "projectTypeName": "税收优惠",
                    "areaName": "青岛市",
                    "methodName": "税收减免",
                    "statusText": "5",
                    "detailStatusName": "待开始",
                    "applyUrl": "javascript:;"
                }
            ]
        }
    }
    
    # 政策解读列表
    read_list_data = {
        "code": 0,
        "obj": {
            "content": [
                {
                    "baseId": "read001",
                    "title": "创业补贴政策解读：如何申请一次性创业补贴",
                    "picUrl": "./images/pic_noDetail.png",
                    "publishTime": "2024-01-20",
                    "policy": {
                        "source": "青岛市人社局"
                    }
                },
                {
                    "baseId": "read002",
                    "title": "大学生创业贷款政策详解及申请流程",
                    "picUrl": "./images/pic_noDetail.png", 
                    "publishTime": "2024-01-18",
                    "policy": {
                        "source": "青岛市财政局"
                    }
                }
            ]
        }
    }
    
    # 政策短视频列表
    video_list_data = {
        "code": 0,
        "obj": {
            "content": [
                {
                    "baseId": "video001",
                    "videoName": "创业补贴申请流程详解",
                    "sysAttachmentVo": None,
                    "coverAttachmentVo": None
                },
                {
                    "baseId": "video002", 
                    "videoName": "大学生创业政策解读",
                    "sysAttachmentVo": None,
                    "coverAttachmentVo": None
                },
                {
                    "baseId": "video003",
                    "videoName": "小微企业扶持政策介绍", 
                    "sysAttachmentVo": None,
                    "coverAttachmentVo": None
                }
            ]
        }
    }
    
    return {
        "regions": regions_data,
        "policy_types": policy_types_data,
        "support_modes": support_modes_data,
        "file_list": file_list_data,
        "calendar_list": calendar_list_data,
        "read_list": read_list_data,
        "video_list": video_list_data
    }

def main():
    # 生成模拟数据
    mock_data = generate_mock_data()
    
    # 创建数据目录
    data_dir = "api_data"
    Path(data_dir).mkdir(exist_ok=True)
    
    # 保存各个数据文件
    for name, data in mock_data.items():
        filename = os.path.join(data_dir, f"{name}.json")
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"已生成: {filename}")
    
    # 保存所有数据到一个文件
    all_data_file = os.path.join(data_dir, "all_data.json")
    with open(all_data_file, 'w', encoding='utf-8') as f:
        json.dump(mock_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n所有模拟数据已生成到: {data_dir}")
    print("现在可以使用这些数据来更新HTML文件")

if __name__ == "__main__":
    main()
