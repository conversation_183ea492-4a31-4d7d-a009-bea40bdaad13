<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="dialogWidth" :close-on-click-modal="false" append-to-body>
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" v-loading="formLoading">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="培训订单" prop="orderId">
            <el-select v-model="formData.orderId" placeholder="请选择培训订单" style="width: 100%" :disabled="dialogType === 'view'">
              <el-option
                v-for="order in trainingOrderOptions"
                :key="order.orderId"
                :label="order.orderTitle"
                :value="order.orderId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报名人姓名" prop="applicantName">
            <el-input v-model="formData.applicantName" placeholder="请输入报名人姓名" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报名人手机号" prop="applicantPhone">
            <el-input v-model="formData.applicantPhone" placeholder="请输入报名人手机号" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报名人邮箱" prop="applicantEmail">
            <el-input v-model="formData.applicantEmail" placeholder="请输入报名人邮箱" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证号" prop="applicantIdCard">
            <el-input v-model="formData.applicantIdCard" placeholder="请输入身份证号" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="applicantGender">
            <el-select v-model="formData.applicantGender" placeholder="请选择性别" style="width: 100%" :disabled="dialogType === 'view'">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="年龄" prop="applicantAge">
            <el-input-number v-model="formData.applicantAge" :min="16" :max="100" placeholder="请输入年龄" style="width: 100%" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="applicantEducation">
            <el-select v-model="formData.applicantEducation" placeholder="请选择学历" style="width: 100%" :disabled="dialogType === 'view'">
              <el-option label="小学" value="小学"></el-option>
              <el-option label="初中" value="初中"></el-option>
              <el-option label="中专" value="中专"></el-option>
              <el-option label="高中" value="高中"></el-option>
              <el-option label="大专" value="大专"></el-option>
              <el-option label="本科" value="本科"></el-option>
              <el-option label="硕士" value="硕士"></el-option>
              <el-option label="博士" value="博士"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="联系地址" prop="applicantAddress">
            <el-input v-model="formData.applicantAddress" placeholder="请输入联系地址" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="工作经验" prop="applicantExperience">
            <el-input v-model="formData.applicantExperience" type="textarea" :rows="3" placeholder="请输入工作经验" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="报名备注" prop="applicationNote">
            <el-input v-model="formData.applicationNote" type="textarea" :rows="3" placeholder="请输入报名备注" :disabled="dialogType === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="dialogType === 'view'">
        <el-col :span="12">
          <el-form-item label="报名状态">
            <el-tag :type="getStatusTagType(formData.applicationStatus)">
              {{ getStatusText(formData.applicationStatus) }}
            </el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报名时间">
            {{ formatDateTime(formData.applicationTime) }}
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="dialogType === 'view' && formData.reviewTime">
        <el-col :span="12">
          <el-form-item label="审核人">
            {{ formData.reviewer || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审核时间">
            {{ formatDateTime(formData.reviewTime) }}
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="dialogType === 'view' && formData.reviewComment">
        <el-col :span="24">
          <el-form-item label="审核意见">
            {{ formData.reviewComment }}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button v-if="dialogType !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { listTrainingOrder } from "@/api/training/order"

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('add')
const dialogWidth = ref('800px')
const formLoading = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const trainingOrderOptions = ref([])

// 表单数据
const formData = ref({
  applicationId: null,
  orderId: null,
  userId: null,
  applicantName: '',
  applicantPhone: '',
  applicantEmail: '',
  applicantIdCard: '',
  applicantGender: '',
  applicantAge: null,
  applicantEducation: '',
  applicantExperience: '',
  applicantAddress: '',
  applicationStatus: '0',
  applicationNote: '',
  reviewer: '',
  reviewComment: '',
  applicationTime: null,
  reviewTime: null
})

// 表单验证规则
const formRules = ref({
  orderId: [
    { required: true, message: '请选择培训订单', trigger: 'change' }
  ],
  applicantName: [
    { required: true, message: '请输入报名人姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入报名人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  applicantEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  applicantIdCard: [
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
})

// 监听props变化
watch(() => props.formOption, (newVal) => {
  if (newVal.dialogWidth) {
    dialogWidth.value = newVal.dialogWidth
  }
}, { immediate: true })

// 方法
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title
  dialogVisible.value = true
  
  // 重置表单数据
  resetFormData()
  
  // 如果是编辑或查看，填充数据
  if (type === 'edit' || type === 'view') {
    Object.assign(formData.value, data)
  }
  
  // 加载培训订单选项
  loadTrainingOrderOptions()
}

const resetFormData = () => {
  formData.value = {
    applicationId: null,
    orderId: null,
    userId: null,
    applicantName: '',
    applicantPhone: '',
    applicantEmail: '',
    applicantIdCard: '',
    applicantGender: '',
    applicantAge: null,
    applicantEducation: '',
    applicantExperience: '',
    applicantAddress: '',
    applicationStatus: '0',
    applicationNote: '',
    reviewer: '',
    reviewComment: '',
    applicationTime: null,
    reviewTime: null
  }
  
  // 清除表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

const loadTrainingOrderOptions = () => {
  listTrainingOrder({ orderStatus: '1' }).then(res => {
    trainingOrderOptions.value = res.rows || []
  }).catch(() => {
    trainingOrderOptions.value = []
  })
}

const handleSubmit = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      
      const submitData = { ...formData.value }
      
      emit('submit', {
        type: dialogType.value,
        data: submitData
      })
    }
  })
}

const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
}

const onSubmitError = () => {
  submitLoading.value = false
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return proxy.parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 暴露方法给父组件
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
