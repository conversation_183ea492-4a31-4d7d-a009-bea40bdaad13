import { parseTime } from "@/utils/ruoyi";

// 字典状态选项
export const dictStatusOptions = [
    {
        label: "正常",
        value: "0",
    },
    {
        label: "停用",
        value: "1",
    },
];

// 数据标签回显样式选项
export const listClassOptions = [
    { value: "default", label: "默认" }, 
    { value: "primary", label: "主要" }, 
    { value: "success", label: "成功" },
    { value: "info", label: "信息" },
    { value: "warning", label: "警告" },
    { value: "danger", label: "危险" }
];

// 字典类型表格配置
export const createDictTypeTableOption = (proxy) => {
    const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

    return {
        dialogWidth: '600px',
        dialogHeight: '70vh',
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "字典编号",
                prop: "dictId",
                width: 100,
                editDisplay: false,
                addDisplay: false,
                viewDisplay: true,
                align: "center"
            },
            {
                label: "字典名称",
                prop: "dictName",
                search: true,
                rules: [{
                    required: true,
                    message: "字典名称不能为空",
                    trigger: "blur"
                }],
                span: 12,
                width: 150,
                showOverflowTooltip: true
            },
            {
                label: "字典类型",
                prop: "dictType",
                search: true,
                rules: [{
                    required: true,
                    message: "字典类型不能为空",
                    trigger: "blur"
                }],
                span: 12,
                width: 150,
                showOverflowTooltip: true,
                slot: true // 在表格中显示为链接
            },
            {
                label: "状态",
                prop: "status",
                type: 'radio',
                span: 24,
                width: 100,
                search: true,
                dicData: sys_normal_disable,
                align: "center",
                defaultValue: "0"
            },
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',
                minRows: 3,
                maxRows: 6,
                span: 24,
                showOverflowTooltip: true
            },
            {
                label: "创建时间",
                prop: "createTime",
                editDisplay: false,
                addDisplay: false,
                type: 'datetime',
                width: 180,
                align: "center",
                searchRange: true,
                formatter: (val, value, label) => parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}')
            }
        ]
    };
};

// 字典数据表格配置
export const createDictDataTableOption = (proxy) => {
    const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

    return {
        dialogWidth: '700px',
        dialogHeight: '75vh',
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "字典编码",
                prop: "dictCode",
                width: 100,
                editDisplay: false,
                addDisplay: false,
                viewDisplay: true,
                align: "center"
            },
            {
                label: "字典类型",
                prop: "dictType",
                editDisplay: false,
                addDisplay: false,
                viewDisplay: true,
                span: 12,
                rules: [{
                    required: true,
                    message: "字典类型不能为空",
                    trigger: "blur"
                }]
            },
            {
                label: "数据标签",
                prop: "dictLabel",
                search: true,
                rules: [{
                    required: true,
                    message: "数据标签不能为空",
                    trigger: "blur"
                }],
                span: 12,
                width: 120,
                slot: true // 在表格中可能有样式显示
            },
            {
                label: "数据键值",
                prop: "dictValue",
                rules: [{
                    required: true,
                    message: "数据键值不能为空",
                    trigger: "blur"
                }],
                span: 12,
                width: 120,
                align: "center"
            },
            {
                label: "显示排序",
                prop: "dictSort",
                type: 'number',
                rules: [{
                    required: true,
                    message: "数据顺序不能为空",
                    trigger: "blur"
                }],
                span: 12,
                width: 100,
                align: "center",
                controlsPosition: "right",
                min: 0,
                defaultValue: 0
            },

            // ==================== 样式配置分组 ====================
            {
                label: "样式配置",
                prop: "divider_style_config",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "回显样式",
                prop: "listClass",
                type: 'select',
                span: 12,
                dicData: listClassOptions,
                defaultValue: "default"
            },
            {
                label: "样式属性",
                prop: "cssClass",
                span: 12,
                placeholder: "请输入样式属性"
            },
            {
                label: "状态",
                prop: "status",
                type: 'radio',
                span: 24,
                width: 100,
                search: true,
                dicData: sys_normal_disable,
                align: "center",
                defaultValue: "0"
            },
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',
                minRows: 3,
                maxRows: 6,
                span: 24
            },
            {
                label: "创建时间",
                prop: "createTime",
                editDisplay: false,
                addDisplay: false,
                type: 'datetime',
                width: 180,
                align: "center",
                formatter: (val, value, label) => parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}')
            }
        ]
    };
};