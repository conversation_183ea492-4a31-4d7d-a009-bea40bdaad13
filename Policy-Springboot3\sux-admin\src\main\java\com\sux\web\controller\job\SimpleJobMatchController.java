package com.sux.web.controller.job;

import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.service.IJobPostingService;
import com.sux.system.service.IWorkerProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 简化的招聘匹配控制器（核心匹配优化版）
 * 提供基础的增删改查和匹配功能
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/job/simple")
public class SimpleJobMatchController extends BaseController {
    
    @Autowired
    private IJobPostingService jobPostingService;
    
    @Autowired
    private IWorkerProfileService workerProfileService;

    /**
     * 简化的招聘信息匹配零工
     * 基于地点、技能、薪资等关键因素进行匹配
     */
    @GetMapping("/postings/{jobId}/match-workers")
    public AjaxResult simpleMatchWorkers(@PathVariable Long jobId, 
                                        @RequestParam(defaultValue = "10") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        
        // 获取所有活跃的零工
        List<WorkerProfile> allWorkers = workerProfileService.selectActiveWorkerProfileList(new WorkerProfile());
        
        // 计算匹配度并排序
        List<Map<String, Object>> matchResults = allWorkers.stream()
                .map(worker -> {
                    double similarity = calculateSimpleMatchScore(jobPosting, worker);
                    Map<String, Object> result = new HashMap<>();
                    result.put("worker", worker);
                    result.put("similarity", similarity);
                    result.put("similarityPercentage", Math.round(similarity * 100));
                    result.put("matchReasons", getMatchReasons(jobPosting, worker));
                    return result;
                })
                .filter(result -> (Double) result.get("similarity") > 0.3) // 只返回匹配度大于30%的
                .sorted((a, b) -> Double.compare((Double) b.get("similarity"), (Double) a.get("similarity")))
                .limit(limit)
                .collect(Collectors.toList());
        
        return success(matchResults);
    }

    /**
     * 简化的零工匹配招聘信息
     */
    @GetMapping("/workers/{workerId}/match-jobs")
    public AjaxResult simpleMatchJobs(@PathVariable Long workerId, 
                                     @RequestParam(defaultValue = "10") Integer limit) {
        WorkerProfile worker = workerProfileService.selectWorkerProfileByWorkerId(workerId);
        if (worker == null) {
            return error("零工信息不存在");
        }
        
        // 获取所有已发布的招聘信息
        List<JobPosting> allJobs = jobPostingService.selectPublishedJobPostingList(new JobPosting());
        
        // 计算匹配度并排序
        List<Map<String, Object>> matchResults = allJobs.stream()
                .map(job -> {
                    double similarity = calculateSimpleMatchScore(job, worker);
                    Map<String, Object> result = new HashMap<>();
                    result.put("job", job);
                    result.put("similarity", similarity);
                    result.put("similarityPercentage", Math.round(similarity * 100));
                    result.put("matchReasons", getMatchReasons(job, worker));
                    return result;
                })
                .filter(result -> (Double) result.get("similarity") > 0.3)
                .sorted((a, b) -> Double.compare((Double) b.get("similarity"), (Double) a.get("similarity")))
                .limit(limit)
                .collect(Collectors.toList());
        
        return success(matchResults);
    }

    /**
     * 计算优化的匹配分数
     * 基于工作类型、工作类别、薪资类型、学历等核心字段进行精准匹配
     */
    private double calculateSimpleMatchScore(JobPosting job, WorkerProfile worker) {
        double score = 0.0;

        // 1. 工作类型匹配 (35%) - 最重要的匹配因素
        score += calculateJobTypeMatch(job.getJobType(), worker.getJobTypesPreferred()) * 0.35;

        // 2. 工作类别匹配 (35%) - 同样重要
        score += calculateJobCategoryMatch(job.getJobCategory(), worker.getWorkCategories()) * 0.35;

        // 3. 薪资类型匹配 (15%) - 薪资结算方式匹配
        score += calculateSalaryTypeMatch(job.getSalaryType(), worker.getSalaryTypePreference()) * 0.15;

        // 4. 薪资范围匹配 (10%) - 薪资数额匹配
        score += calculateSalaryRangeMatch(job, worker) * 0.10;

        // 5. 学历匹配 (5%) - 基础要求
        score += calculateEducationMatch(job.getEducationRequired(), worker.getEducationLevel()) * 0.05;

        return Math.min(1.0, score);
    }

    /**
     * 工作类别匹配计算
     */
    private double calculateJobCategoryMatch(String jobCategory, String workerCategories) {
        if (jobCategory == null || workerCategories == null) {
            return 0.0;
        }

        // 完全匹配
        if (workerCategories.contains(jobCategory)) {
            return 1.0;
        }

        // 相关类别匹配
        Map<String, List<String>> relatedCategories = getRelatedCategories();
        List<String> relatedList = relatedCategories.get(jobCategory);
        if (relatedList != null) {
            for (String related : relatedList) {
                if (workerCategories.contains(related)) {
                    return 0.7;
                }
            }
        }

        return 0.0;
    }

    /**
     * 获取相关工作类别映射
     */
    private Map<String, List<String>> getRelatedCategories() {
        Map<String, List<String>> related = new HashMap<>();
        related.put("服务员", Arrays.asList("收银员", "前台接待", "客服"));
        related.put("保洁", Arrays.asList("家政服务", "清洁工"));
        related.put("搬运工", Arrays.asList("装卸工", "快递员", "配送员"));
        related.put("销售", Arrays.asList("客服", "市场推广", "业务员"));
        related.put("厨师助手", Arrays.asList("配菜员", "洗碗工", "后厨"));
        return related;
    }

    /**
     * 工作类型匹配计算
     */
    private double calculateJobTypeMatch(String jobType, String workerJobTypesPreferred) {
        if (jobType == null || workerJobTypesPreferred == null) {
            return 0.0;
        }

        // 完全匹配
        if (workerJobTypesPreferred.contains(jobType)) {
            return 1.0;
        }

        // 兼容性匹配
        if ((jobType.equals("兼职") || jobType.equals("临时工")) &&
            (workerJobTypesPreferred.contains("兼职") || workerJobTypesPreferred.contains("临时工"))) {
            return 0.8;
        }

        // 全职和兼职的兼容性
        if (jobType.equals("全职") && workerJobTypesPreferred.contains("兼职")) {
            return 0.6;
        }

        return 0.0;
    }

    /**
     * 薪资类型匹配计算
     */
    private double calculateSalaryTypeMatch(String jobSalaryType, String workerSalaryTypePreference) {
        if (jobSalaryType == null || workerSalaryTypePreference == null) {
            return 0.5; // 默认中等匹配
        }

        // 完全匹配
        if (jobSalaryType.equals(workerSalaryTypePreference)) {
            return 1.0;
        }

        // 兼容性匹配
        Map<String, List<String>> compatibleTypes = getCompatibleSalaryTypes();
        List<String> compatibleList = compatibleTypes.get(jobSalaryType);
        if (compatibleList != null && compatibleList.contains(workerSalaryTypePreference)) {
            return 0.7;
        }

        return 0.0;
    }

    /**
     * 获取兼容的薪资类型映射
     */
    private Map<String, List<String>> getCompatibleSalaryTypes() {
        Map<String, List<String>> compatible = new HashMap<>();
        compatible.put("hourly", Arrays.asList("daily", "piece"));
        compatible.put("daily", Arrays.asList("hourly", "piece"));
        compatible.put("monthly", Arrays.asList("daily"));
        compatible.put("piece", Arrays.asList("hourly", "daily"));
        return compatible;
    }

    /**
     * 技能匹配计算
     */
    private double calculateSkillMatch(String jobSkills, String workerSkills) {
        if (jobSkills == null || workerSkills == null) {
            return 0.5;
        }

        String[] jobSkillArray = jobSkills.toLowerCase().split("[,，、\\s]+");
        String[] workerSkillArray = workerSkills.toLowerCase().split("[,，、\\s]+");

        int matchCount = 0;
        for (String jobSkill : jobSkillArray) {
            for (String workerSkill : workerSkillArray) {
                if (jobSkill.contains(workerSkill) || workerSkill.contains(jobSkill)) {
                    matchCount++;
                    break;
                }
            }
        }

        return jobSkillArray.length > 0 ? (double) matchCount / jobSkillArray.length : 0.0;
    }

    /**
     * 薪资范围匹配计算（优化版）
     */
    private double calculateSalaryRangeMatch(JobPosting job, WorkerProfile worker) {
        if (job.getSalaryMin() == null || worker.getSalaryExpectationMin() == null) {
            return 0.5; // 如果薪资信息不完整，给予中等分数
        }

        double jobMin = job.getSalaryMin().doubleValue();
        double jobMax = job.getSalaryMax() != null ? job.getSalaryMax().doubleValue() : jobMin * 1.2;
        double workerMin = worker.getSalaryExpectationMin().doubleValue();
        double workerMax = worker.getSalaryExpectationMax() != null ?
                          worker.getSalaryExpectationMax().doubleValue() : workerMin * 1.2;

        // 检查薪资范围是否有重叠
        double overlapMin = Math.max(jobMin, workerMin);
        double overlapMax = Math.min(jobMax, workerMax);

        if (overlapMax >= overlapMin) {
            // 有重叠，计算重叠程度
            double overlapRange = overlapMax - overlapMin;
            double jobRange = jobMax - jobMin;
            double workerRange = workerMax - workerMin;
            double avgRange = (jobRange + workerRange) / 2;

            if (avgRange > 0) {
                return Math.min(1.0, overlapRange / avgRange);
            } else {
                return 1.0; // 完全匹配
            }
        } else {
            // 无重叠，计算距离惩罚
            double gap = overlapMin - overlapMax;
            double avgSalary = (jobMin + jobMax + workerMin + workerMax) / 4;
            double penalty = gap / avgSalary;
            return Math.max(0.0, 1.0 - penalty);
        }
    }

    /**
     * 学历匹配计算
     */
    private double calculateEducationMatch(String jobEducation, String workerEducation) {
        if (jobEducation == null || workerEducation == null) {
            return 0.5;
        }

        // 学历等级映射
        Map<String, Integer> educationLevels = getEducationLevels();
        Integer jobLevel = educationLevels.get(jobEducation);
        Integer workerLevel = educationLevels.get(workerEducation);

        if (jobLevel == null || workerLevel == null) {
            return 0.5;
        }

        // 如果招聘要求"不限"，完全匹配
        if ("不限".equals(jobEducation)) {
            return 1.0;
        }

        // 零工学历高于或等于要求，完全匹配
        if (workerLevel >= jobLevel) {
            return 1.0;
        }

        // 零工学历低于要求，根据差距给分
        int gap = jobLevel - workerLevel;
        return Math.max(0.0, 1.0 - gap * 0.2);
    }

    /**
     * 获取学历等级映射
     */
    private Map<String, Integer> getEducationLevels() {
        Map<String, Integer> levels = new HashMap<>();
        levels.put("不限", 0);
        levels.put("初中", 1);
        levels.put("高中", 2);
        levels.put("中专", 2);
        levels.put("大专", 3);
        levels.put("本科", 4);
        levels.put("硕士", 5);
        levels.put("博士", 6);
        return levels;
    }

    /**
     * 获取匹配原因（优化版）
     */
    private List<String> getMatchReasons(JobPosting job, WorkerProfile worker) {
        List<String> reasons = new ArrayList<>();

        // 工作类型匹配原因
        double jobTypeScore = calculateJobTypeMatch(job.getJobType(), worker.getJobTypesPreferred());
        if (jobTypeScore > 0.8) {
            reasons.add("工作类型完全匹配");
        } else if (jobTypeScore > 0.6) {
            reasons.add("工作类型部分匹配");
        }

        // 工作类别匹配原因
        double categoryScore = calculateJobCategoryMatch(job.getJobCategory(), worker.getWorkCategories());
        if (categoryScore > 0.8) {
            reasons.add("工作类别完全匹配");
        } else if (categoryScore > 0.6) {
            reasons.add("工作类别相关匹配");
        }

        // 薪资类型匹配原因
        double salaryTypeScore = calculateSalaryTypeMatch(job.getSalaryType(), worker.getSalaryTypePreference());
        if (salaryTypeScore > 0.8) {
            reasons.add("薪资类型匹配");
        } else if (salaryTypeScore > 0.6) {
            reasons.add("薪资类型兼容");
        }

        // 薪资范围匹配原因
        double salaryRangeScore = calculateSalaryRangeMatch(job, worker);
        if (salaryRangeScore > 0.7) {
            reasons.add("薪资期望匹配");
        } else if (salaryRangeScore > 0.5) {
            reasons.add("薪资期望接近");
        }

        // 学历匹配原因
        double educationScore = calculateEducationMatch(job.getEducationRequired(), worker.getEducationLevel());
        if (educationScore > 0.9) {
            reasons.add("学历要求匹配");
        } else if (educationScore > 0.7) {
            reasons.add("学历基本符合");
        }

        if (reasons.isEmpty()) {
            reasons.add("基础条件匹配");
        }

        return reasons;
    }

    /**
     * 基于核心字段的招聘信息搜索
     */
    @GetMapping("/search-jobs")
    public AjaxResult searchJobsByCore(@RequestParam(required = false) String keyword,
                                      @RequestParam(required = false) String jobType,
                                      @RequestParam(required = false) String jobCategory,
                                      @RequestParam(required = false) String salaryType,
                                      @RequestParam(required = false) String educationRequired,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize) {

        JobPosting searchJob = new JobPosting();
        if (keyword != null && !keyword.trim().isEmpty()) {
            searchJob.setJobTitle(keyword);
        }
        if (jobType != null && !jobType.trim().isEmpty()) {
            searchJob.setJobType(jobType);
        }
        if (jobCategory != null && !jobCategory.trim().isEmpty()) {
            searchJob.setJobCategory(jobCategory);
        }
        if (salaryType != null && !salaryType.trim().isEmpty()) {
            searchJob.setSalaryType(salaryType);
        }
        if (educationRequired != null && !educationRequired.trim().isEmpty()) {
            searchJob.setEducationRequired(educationRequired);
        }

        // 只搜索已发布的招聘信息
        searchJob.setStatus("published");

        List<JobPosting> jobList = jobPostingService.selectJobPostingList(searchJob);

        // 简单分页处理
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, jobList.size());
        List<JobPosting> pagedList = start < jobList.size() ? jobList.subList(start, end) : new ArrayList<>();

        Map<String, Object> result = new HashMap<>();
        result.put("rows", pagedList);
        result.put("total", jobList.size());
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);

        return success(result);
    }

    /**
     * 获取匹配统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getMatchStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 获取基础统计
        List<JobPosting> allJobs = jobPostingService.selectPublishedJobPostingList(new JobPosting());
        List<WorkerProfile> allWorkers = workerProfileService.selectActiveWorkerProfileList(new WorkerProfile());

        stats.put("totalJobs", allJobs.size());
        stats.put("totalWorkers", allWorkers.size());

        // 计算平均匹配度
        double totalMatchScore = 0.0;
        int matchCount = 0;

        for (JobPosting job : allJobs.subList(0, Math.min(10, allJobs.size()))) {
            for (WorkerProfile worker : allWorkers.subList(0, Math.min(10, allWorkers.size()))) {
                double score = calculateSimpleMatchScore(job, worker);
                if (score > 0.3) {
                    totalMatchScore += score;
                    matchCount++;
                }
            }
        }

        stats.put("averageMatchScore", matchCount > 0 ? totalMatchScore / matchCount : 0.0);
        stats.put("highQualityMatches", matchCount);

        return success(stats);
    }

    // ==================== 基础增删改查接口 ====================

    /**
     * 获取招聘信息详情
     */
    @GetMapping("/job/{jobId}")
    public AjaxResult getJobDetail(@PathVariable Long jobId) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        // 增加浏览次数
        jobPostingService.increaseViewCount(jobId);
        return success(jobPosting);
    }

    /**
     * 获取零工信息详情
     */
    @GetMapping("/worker/{workerId}")
    public AjaxResult getWorkerDetail(@PathVariable Long workerId) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        return success(workerProfile);
    }

    /**
     * 获取所有工作类型选项
     */
    @GetMapping("/options/job-types")
    public AjaxResult getJobTypeOptions() {
        List<String> jobTypes = jobPostingService.selectAllJobTypes();
        return success(jobTypes);
    }

    /**
     * 获取所有工作类别选项
     */
    @GetMapping("/options/job-categories")
    public AjaxResult getJobCategoryOptions() {
        List<String> jobCategories = jobPostingService.selectAllJobCategories();
        return success(jobCategories);
    }

    /**
     * 获取所有薪资类型选项
     */
    @GetMapping("/options/salary-types")
    public AjaxResult getSalaryTypeOptions() {
        List<String> salaryTypes = jobPostingService.selectAllSalaryTypes();
        return success(salaryTypes);
    }

    /**
     * 获取所有学历要求选项
     */
    @GetMapping("/options/education-requirements")
    public AjaxResult getEducationRequirementOptions() {
        List<String> educationRequirements = jobPostingService.selectAllEducationRequirements();
        return success(educationRequirements);
    }

    /**
     * 获取所有零工偏好工作类型选项
     */
    @GetMapping("/options/worker-job-types")
    public AjaxResult getWorkerJobTypeOptions() {
        List<String> jobTypes = workerProfileService.selectAllJobTypesPreferred();
        return success(jobTypes);
    }

    /**
     * 获取所有零工工作类别偏好选项
     */
    @GetMapping("/options/worker-categories")
    public AjaxResult getWorkerCategoryOptions() {
        List<String> workCategories = workerProfileService.selectAllWorkCategories();
        return success(workCategories);
    }

    /**
     * 基于核心字段的招聘信息列表
     */
    @GetMapping("/jobs/core-search")
    public AjaxResult getCoreJobList(@RequestParam(required = false) String jobType,
                                    @RequestParam(required = false) String jobCategory,
                                    @RequestParam(required = false) String salaryType,
                                    @RequestParam(required = false) String educationRequired,
                                    @RequestParam(required = false) String keyword,
                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                    @RequestParam(defaultValue = "10") Integer pageSize) {

        List<JobPosting> jobList = jobPostingService.selectJobPostingByCoreFields(
            jobType, jobCategory, salaryType, educationRequired, keyword);

        // 简单分页处理
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, jobList.size());
        List<JobPosting> pagedList = start < jobList.size() ? jobList.subList(start, end) : new ArrayList<>();

        Map<String, Object> result = new HashMap<>();
        result.put("rows", pagedList);
        result.put("total", jobList.size());
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);

        return success(result);
    }

    /**
     * 基于核心字段的零工信息列表
     */
    @GetMapping("/workers/core-search")
    public AjaxResult getCoreWorkerList(@RequestParam(required = false) String jobTypesPreferred,
                                       @RequestParam(required = false) String workCategories,
                                       @RequestParam(required = false) String salaryTypePreference,
                                       @RequestParam(required = false) String educationLevel,
                                       @RequestParam(required = false) String keyword,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize) {

        List<WorkerProfile> workerList = workerProfileService.selectWorkerProfileByCoreFields(
            jobTypesPreferred, workCategories, salaryTypePreference, educationLevel, keyword);

        // 简单分页处理
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, workerList.size());
        List<WorkerProfile> pagedList = start < workerList.size() ? workerList.subList(start, end) : new ArrayList<>();

        Map<String, Object> result = new HashMap<>();
        result.put("rows", pagedList);
        result.put("total", workerList.size());
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);

        return success(result);
    }
}
