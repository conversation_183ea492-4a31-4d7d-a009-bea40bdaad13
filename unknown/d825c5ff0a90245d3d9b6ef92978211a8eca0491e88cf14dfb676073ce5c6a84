-- 为政策申请表添加申请人姓名和手机号字段
-- 执行时间：2025-07-21

-- 添加申请人姓名字段
ALTER TABLE `policy_application` 
ADD COLUMN `applicant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人姓名' AFTER `applicant_user_id`;

-- 添加申请人手机号字段
ALTER TABLE `policy_application` 
ADD COLUMN `applicant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人手机号' AFTER `applicant_name`;

-- 为新字段添加索引（可选，用于查询优化）
ALTER TABLE `policy_application` 
ADD INDEX `idx_applicant_name`(`applicant_name`) USING BTREE,
ADD INDEX `idx_applicant_phone`(`applicant_phone`) USING BTREE;

-- 更新表注释
ALTER TABLE `policy_application` COMMENT = '政策申请表（包含申请人基本信息）';
