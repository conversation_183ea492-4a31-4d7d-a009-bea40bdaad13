<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找场地-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202503281048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <div class="">
        <!-- banner 开始 -->
        <div class="bannerBox pr none">
            <!-- ko if:bagnnerList().length>0 -->
            <div class="bannerSlide" data-bind="foreach:bagnnerList">
                <img src="./image/index_banner.png" data-bind="attr:{src:fullPath}">
            </div>
            <div class="hd" data-bind="visible:bagnnerList().length>1"><ul></ul></div>
            <!-- /ko -->
            <!-- ko if:bagnnerList().length==0 -->
            <div class="bannerSlide" >
                <img src="./image/index_banner.png" >
            </div>
            <!-- /ko -->
            <div class="conAuto2 pr">
                <!-- slideTxtBox start -->
                <!-- ko if:cdggList().length>0 -->
                <div class="slideTxtBox pa pr">
                    <div class="bd">
                        <ul data-bind="foreach:cdggList()">
                            <li class="pr">
                                <div class="sliLiTop clearfix">
                                    <div class="fl date">
                                        <p class="top" data-bind="text:date"></p>
                                        <p class="bottom" data-bind="text:baseCreateTime"></p>
                                    </div>
                                    <a href="javascript:;"
                                        data-bind="text:baseName,attr:{title:baseName,href:'./placeDetail.html?pageType='+baseId+'&id='+parkId}"
                                        class="block fl title transi paraoverflow2"></a>
                                </div>
                                <div class="mainText paraoverflow3" data-bind="html:parkNoticeDetail"></div>
                                <a href="javascript:;" class="pa animationBtn" style="left:0px;bottom:0px" target="_blank"
                                    data-bind="attr:{href:'./placeDetail.html?pageType='+baseId+'&id='+parkId}">
                                    <img src="./image/index_more.png">
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div data-bind="foreach:cdggList()">
                        <div class="hd clearfix pa">
                            <div class="fr">
                                <a href="javascript:;" class="prev fl transi"></a>
                                <p class="pageState fl mt2"></p>
                                <a href="javascript:;" class="next fr transi"></a>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /ko -->
                <!-- slideTxtBox end -->

            </div>
        </div>
        <!-- banner end -->

        <!-- bannerNew  -->
         <div class="bannerNew"></div>
        <!-- bannerNew end -->

        <!-- 第一部分 start -->
        <div class="conAuto2 cycdBox">
            <ul class="clearfix numout">
                <li class="fl cp bg1" onclick="linkPage(1)">
                    <div id="onerun01" class="num"></div>
                    <p class="text">场地数量（个）</p>
                </li>
                <li class="fl  bg2">
                    <div id="onerun02" class="num"></div>
                    <p class="text">场地面积（㎡）</p>
                </li>
                <li class="fl  bg3">
                    <div id="onerun03" class="num"></div>
                    <p class="text">可使用面积（㎡）</p>
                </li>
                <li class="fl  bg4">
                    <div id="onerun04" class="num"></div>
                    <p class="text">已入驻企业（家）</p>
                </li>
            </ul>
            <div class="conAuto2 clearfix  mt40">
                <div class="fl yqdtOut">
                    <div class="clearfix">
                        <div class="fl">
                            <p class="contentTitle">园区<em>动态</em></p>
                            <p class="xg"></p>
                        </div>
                        <a href="informationList.html"  class="moreBtn block fr transi ">
                            <span class="inlineblock text-white">更多</span>
                        </a>
                    </div>
                    <div class="yqdtMain">
                        <ul data-bind="foreach:parkNoticeList">
                            <li class="clearfix transi">
                                <a class="textoverflow transi" target="_blank" href="javascript:;" data-bind="text:title,attr:{title:title,href:'informationDetail.html?id='+baseId}"></a>
                                <span data-bind="text:baseCreateTime"></span>
                            </li>
                        </ul>
                        <!-- <ul>
                            <li class="clearfix transi">
                                <a class="textoverflow transi" target="_blank" href="https://hrss.qingdao.gov.cn/cy/index/informationDetail.html?id=a2952a9df41d476b8652b3d41c497335">湛山创客工厂入驻企业招募公告</a>
                                <span>2024-12-13</span>
                            </li>
                        </ul> -->
                    </div>
                </div>
                <div class="fr  cycdRkBox">
                    <a href="./placeMap.html" target="_blank" class="block animationBtn">立即查看</a>
                </div>
                

            </div>
        </div>
        <!-- 第一部分 end -->
        <!-- 第三部分 start -->
        <div class="cycdMainBox">
            <div class="conAuto2">
                <div class="clearfix mb20">
                    <div class="fl">
                        <p class="contentTitle">创业<em>场地</em></p>
                        <p class="xg"></p>
                    </div>
                    <a href="javascript:;" onclick="moreList()"  class="moreBtn block fr transi mt20">
                        <span class="inlineblock text-white">更多</span>
                    </a>
                </div>
                <!-- 下拉筛选 -->
                <div class="selectBox clearfix">
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module1">
                            <div class="title ">场地区域</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:positionName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:positionList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('0',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module2">
                            <div class="title ">场地类型</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:typeName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:typeList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('1',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module3">
                            <div class="title ">场地面积</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:areaName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:areaList01()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('2',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module4">
                            <div class="title ">场地等级</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:levelName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:levelList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('3',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="fl pr mr10 selectContent">
                        <div class="selectModule clearfix module5">
                            <div class="title ">行业方向</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:directionName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none">
                            <i class="arrowIcon"></i>
                            <ul data-bind="foreach:directionList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('4',event)}"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="fr pr">
                        <div class="selectModule clearfix">
                            <div class="title ">运营模式</div>
                            <div class="name ">
                                <p class="textoverflow" data-bind="text:moneyName"></p>
                            </div>
                        </div>
                        <div class=" selectList pa none" style="right: 0;">
                            <i class="arrowIcon" style="left: 60%;"></i>
                            <ul data-bind="foreach:moneyList()">
                                <li data-bind="text:baseName,click:function(event){$parent.selectData('5',event)}"></li>
                            </ul>
                        </div>
                    
                    </div>
                </div>
                <!-- 下拉筛选 -->
                
                <ul class="clearfix cycdList" data-bind="foreach:cycdList()">
                    <li class="fl transi pr">
                        <!-- <em class="tabs pa">高能级园区</em> -->
                        <a class=" block" href="javascript:;" target="_blank"
                        data-bind="attr:{href:'./placeDetail.html?id='+baseId}">
                        <p  class="title block textoverflow"
                            data-bind="text:parkName,attr:{title:parkName}"></p>
                            <div class="clearfix">
                                <div class="fl mainLeft">
                                
                                    <div class="clearfix">
                                        <p class="fl textoverflow bq1" data-bind="text:parkType,visible:parkType"></p>
                                        <p class="fl textoverflow bq2" data-bind="text:parkLevel,visible:parkLevel"></p>
                                    </div>
                                    <p class="liText textoverflow mt10">可使用面积：<span data-bind="text:acreage+'平方米'"></p>
                                    <p class="liText textoverflow">已入驻企业：<span data-bind="text:rzCompanyCount+'家'"></span>
                                    </p>
                                    <p class="liText textoverflow">招商时间：
                                       <!-- ko if:isOpenSettle=='0' -->
                                       <span data-bind="text:applyTimeStatus==0?'长期':((applyStartDate?applyStartDate.substring(0,10):'--')+'至'+(applyEndDate?applyEndDate.substring(0,10):'--'))"></span>
                                       <!-- /ko -->
                                       <!-- ko if:isOpenSettle=='1' -->
                                       定期招商，暂未开放
                                       <!-- /ko -->
                                    </p>
                                    
                                </div>
                                <p  class="fr block imgA transi">
                                    <!-- ko if:imageUrl -->
                                    <img src="" class="mainImg transi" data-bind="attr:{src:imageUrl}">
                                    <!-- /ko -->
                                    <!-- ko if:!imageUrl -->
                                    <img src="../public/images/pics/pic_noList.png" class="mainImg transi">
                                    <!-- /ko -->
                                </p>
                            </div>
                            
                            <p class="pos textoverflow" data-bind="text:address,attr:{title:address}">
                            </p>
                        </a>
                    </li>
                </ul>
                <!-- 暂无数据 -->
                <div class="nodataPic none nodataPicCycd"></div>
            </div>
        </div>
        <!-- 第三部分 end -->
        <!-- 第四部分 start -->
        <!-- <div class="rzlcBox">
            <div class="conAuto2">
                <div>
                    <p class="contentTitle">场地服务商<em>认证流程</em></p>
                    <p class="xg"></p>
                </div>
                <div class="mt50">
                    <div class="clearfix ulDiv">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg1.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">1</p>
                                <p class="f18 text-gray3 mt10 ml10">场地服务商登录</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg2.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">2</p>
                                <p class="f18 text-gray3 mt10 ml10">在线实名认证</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi">
                            <img src="./image/index4LiImg3.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">3</p>
                                <p class="f18 text-gray3 mt10 ml10">审核</p>
                            </div>
                        </div>
                        <img src="./image/index4Jt.png" class="fl index4JtImg">
                        <div class="clearfix fl liDiv transi mr0">
                            <img src="./image/index4LiImg4.png" class="fl">
                            <div class="fl text-white">
                                <p class="ml20 ml22">4</p>
                                <p class="f18 text-gray3 mt10 ml10">认证成功</p>
                            </div>
                        </div>
                        <a href="javascript:;" class="fl block ml20 animationBtn" onclick="sqrzFun()">
                            <img src="./image/index4Ljrz.png">
                        </a>
                    </div>
                </div>
            </div>
        </div> -->
        <!-- 第四部分 end -->
        <!-- 第五部分 start -->
        <div class="cdxqBox">
            <div class="conAuto2">
                <div class="clearfix">
                    <div class="fl">
                        <p class="contentTitle">场地<em>需求</em></p>
                        <p class="xg2"></p>
                    </div>
                    <a href="./demandList.html" target="_blank" class="moreBtn block fr transi mt20">
                        <span class="inlineblock text-white">更多</span>
                    </a>
                </div>
                <div class="clearfix mt20 w1450">
                    <div class="fl w1090">
                        <ul class="clearfix" data-bind="foreach:cdxqList()">
                            <li class="fl transi pr">
                                <span class="tabs" data-bind="text:followStatus==0?'待对接':(followStatus==1?'已对接':'跟进中'),css:{tab1:followStatus==0,tab2:followStatus==1,tab3:followStatus==2}"></span>
                                <a href="javascript:;" class="block" target="_blank" data-bind="attr:{href:'./demandDet.html?id='+baseId}">
                                    <div class="clearfix">
                                        <p class="textoverflow title transi fl"
                                        data-bind="text:title,attr:{title:title}"></p>
                                        <img class="fr icons" src="./image/index_xqIcon.png" >
                                    </div>
                                    
                                    <p class="paraoverflow2 mainText"
                                        data-bind="text:demandIntroduct,attr:{title:demandIntroduct}">
                                        ...
                                    </p>
                                    <div class="clearfix bt">
                                        <p class="fl f16 fb text-gray6 mt15" data-bind="text:createTime"></p>
                                        <p class="block fr ljgd animationBtn"></p>
                                    </div>
                                </a>
                            </li>
                        </ul>
                        <!-- 暂无数据 -->
                        <div class="nodataPic none nodataPicCdxq"></div>
                    </div>

                    <div class="mainRight fr">
                        <a href="javascript:;" class="block fbNow animationBtn transi" onclick="fbxqNow()">立即发布</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- 第五部分 end -->
        <!-- 园区招商需求 start -->
        <div class="qyzsBox">
            <div class="conAuto2">
                <div class="clearfix">
                    <div class="fl">
                        <p class="contentTitle">园区招商<em>需求</em></p>
                        <p class="xg2"></p>
                    </div>
                    <a href="./attractList.html" target="_blank" class="moreBtn block fr transi mt20">
                        <span class="inlineblock text-white">更多</span>
                    </a>
                </div>
                <ul class="clearfix" data-bind="foreach:qyzsList">
                    <li class="fl transi pr" data-bind="click:$root.yqzsInfo">
                        <em class="pa parkTypeName textoverflow" data-bind="text:parkTypeName"></em>
                        <p class="title paraoverflow2" data-bind="text:title,attr:{title:title}"></p>
                        <p class="infop textoverflow">行业方向：<span data-bind="text:industryName"></span></p>
                        <p class="infop textoverflow">招商场地面积：<span data-bind="text:areaMin+'-'+areaMax+'平方米'"></span></p>
                        <p class="infop textoverflow">招商场地位置：<span data-bind="text:areaName"></span></p>
                        <span class="btns transi">立即查看</span>
                    </li>
                </ul>
                <div class="nodataPic none nodataPicQyzs" style="margin: 20px auto 0;"></div>
            </div>
        </div>
        <!-- 园区招商需求 end -->
    </div>
    <!-- main end -->
    <!-- 底部 开始 -->
    <div id="footerBar"> </div>
    <!-- 底部 开始 -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <script src="../public/numberRun/numberRunAll.js" type="text/javascript" charset="utf-8"></script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/index.js?v=202503261407" type="text/javascript" charset="utf-8"></script>
</body>

</html>