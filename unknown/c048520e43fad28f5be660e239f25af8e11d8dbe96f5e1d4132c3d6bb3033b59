// 任务日志表格和表单配置
export function createJobLogTableOption(proxy) {
   const { sys_common_status, sys_job_group } = proxy.useDict("sys_common_status", "sys_job_group");
   
   return {
      // 表格列配置
      column: [
         {
            label: "日志编号",
            prop: "jobLogId",
            width: 100,
            align: "center",
            sortable: true
         },
         {
            label: "任务名称",
            prop: "jobName",
            minWidth: 120,
            align: "center",
            showOverflowTooltip: true,
            search: true,
            searchType: "input",
            searchPlaceholder: "请输入任务名称"
         },
         {
            label: "任务组名",
            prop: "jobGroup",
            width: 120,
            align: "center",
            showOverflowTooltip: true,
            search: true,
            searchType: "select",
            searchPlaceholder: "请选择任务组名",
            dicData: sys_job_group,
            slot: true
         },
         {
            label: "调用目标",
            prop: "invokeTarget",
            minWidth: 150,
            align: "center",
            showOverflowTooltip: true
         },
         {
            label: "日志信息",
            prop: "jobMessage",
            minWidth: 150,
            align: "center",
            showOverflowTooltip: true
         },
         {
            label: "执行状态",
            prop: "status",
            width: 100,
            align: "center",
            search: true,
            searchType: "select",
            searchPlaceholder: "请选择执行状态",
            dicData: sys_common_status,
            slot: true
         },
         {
            label: "执行时间", 
            prop: "createTime",
            width: 180,
            align: "center",
            type: "datetime",
            format: "YYYY-MM-DD HH:mm:ss",
            search: true,
            searchType: "daterange",
            searchPlaceholder: "请选择执行时间"
         }
      ],
   };
} 