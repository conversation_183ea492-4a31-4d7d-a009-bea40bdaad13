# Vue Template 字段配置说明

## 概述

Vue Template 是一个基于配置的动态表单和表格系统，通过简单的 JSON 配置即可快速生成功能完整的 CRUD 页面。

## 基础配置结构

```javascript
export const createTemplateTableOption = (proxy) => {
    return {
        // 弹窗配置
        dialogWidth: '1000px',     // 弹窗宽度
        dialogHeight: '70vh',      // 弹窗内容区最大高度
        labelWidth: '120px',       // 表单标签宽度
        
        // 字段配置
        column: [
            // 字段配置项...
        ]
    };
};
```

## 字段配置选项

### 基础属性

| 属性名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `label` | String | ✓ | 字段显示标签 |
| `prop` | String | ✓ | 字段属性名，对应数据模型的键 |
| `type` | String | ✓ | 字段类型，决定渲染的组件 |
| `span` | Number | - | 表单栅格占用列数（1-24） |
| `minWidth` | Number | - | 表格列最小宽度 |
| `width` | Number | - | 表格列固定宽度 |

### 显示控制

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `addDisplay` | Boolean | true | 新增时是否显示 |
| `editDisplay` | Boolean | true | 编辑时是否显示 |
| `viewDisplay` | Boolean | true | 查看时是否显示 |
| `search` | Boolean | false | 是否在搜索栏显示 |
| `searchWidth` | String | - | 搜索框宽度 |

### 表格相关

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `sortable` | Boolean | false | 表格列是否可排序 |
| `showOverflowTooltip` | Boolean | false | 内容过长时显示 tooltip |
| `fixed` | String | - | 列是否固定（'left'/'right'） |

### 验证规则

```javascript
{
    label: "用户名称",
    prop: "userName",
    type: 'input',
    rules: [{
        required: true,
        message: "请输入用户名称",
        trigger: "blur"
    }, {
        min: 2,
        max: 20,
        message: "用户名长度在 2 到 20 个字符",
        trigger: "blur"
    }]
}
```

## 字段类型详解

### 1. 输入框 (input)

基础的文本输入框，支持多种验证和格式化选项。

```javascript
{
    label: "用户名称",
    prop: "userName",
    type: 'input',
    span: 12,
    search: true,
    placeholder: "请输入用户名称",
    maxlength: 30,
    showWordLimit: true,
    clearable: true,
    rules: [{
        required: true,
        message: "请输入用户名称",
        trigger: "blur"
    }]
}
```

**特有属性：**
- `maxlength`: 最大输入长度
- `showWordLimit`: 显示字数统计
- `clearable`: 是否可清空
- `placeholder`: 占位符文本

### 2. 密码框 (password)

用于密码输入的字段，具有密码掩码功能。

```javascript
{
    label: "密码",
    prop: "password",
    type: 'password',
    span: 12,
    addDisplay: true,
    editDisplay: false,
    viewDisplay: false,
    rules: [{
        required: true,
        message: "请输入密码",
        trigger: "blur"
    }]
}
```

### 3. 文本域 (textarea)

多行文本输入框，适合长文本内容。

```javascript
{
    label: "备注",
    prop: "remark",
    type: 'textarea',
    span: 24,
    rows: 4,
    autosize: { minRows: 2, maxRows: 6 },
    maxlength: 500,
    showWordLimit: true,
    placeholder: "请输入备注信息"
}
```

**特有属性：**
- `rows`: 文本域行数
- `autosize`: 自适应高度配置

### 4. 数字输入框 (number)

专门用于数字输入的字段，支持数值验证和步长控制。

```javascript
{
    label: "年龄",
    prop: "age",
    type: 'number',
    span: 12,
    min: 0,
    max: 150,
    step: 1,
    precision: 0,
    controls: true,
    defaultValue: 18
}
```

**特有属性：**
- `min`: 最小值
- `max`: 最大值
- `step`: 步长
- `precision`: 精度（小数位数）
- `controls`: 是否显示控制按钮

### 5. 带后缀数字输入框 (number-suffix)

数字输入框的变体，支持后缀文本显示。

```javascript
{
    label: "价格",
    prop: "price",
    type: 'number-suffix',
    span: 12,
    min: 0,
    precision: 2,
    suffix: "元"
}
```

**特有属性：**
- `suffix`: 后缀文本

### 6. 下拉选择框 (select)

用于单选或多选的下拉框组件。

```javascript
{
    label: "状态",
    prop: "status",
    type: 'select',
    span: 12,
    search: true,
    dicData: sys_normal_disable,
    clearable: true,
    filterable: true,
    multiple: false,
    searchMultiple: true,
    collapseTags: true
}
```

**特有属性：**
- `dicData`: 选项数据源
- `multiple`: 是否多选
- `filterable`: 是否可搜索
- `searchMultiple`: 搜索时是否支持多选
- `collapseTags`: 多选时是否折叠标签

### 7. 树选择器 (tree-select)

用于层级数据选择的树形选择器。

```javascript
{
    label: "组织架构",
    prop: "orgId",
    type: 'tree-select',
    span: 12,
    dicData: treeOptions,
    checkStrictly: false,
    multiple: false
}
```

**特有属性：**
- `checkStrictly`: 是否严格的遵守父子节点不互相关联
- `multiple`: 是否多选

### 8. 单选框组 (radio)

用于单选的选项组。

```javascript
{
    label: "性别",
    prop: "sex",
    type: 'radio',
    span: 12,
    dicData: sys_user_sex,
    button: false,
    size: 'default'
}
```

**特有属性：**
- `button`: 是否使用按钮样式
- `size`: 尺寸大小

### 9. 多选框组 (checkbox)

用于多选的选项组。

```javascript
{
    label: "兴趣爱好",
    prop: "hobbies",
    type: 'checkbox',
    span: 12,
    dicData: [
        { label: "阅读", value: "reading" },
        { label: "运动", value: "sports" }
    ],
    button: false
}
```

### 10. 开关 (switch)

布尔值开关组件。

```javascript
{
    label: "是否启用",
    prop: "isEnabled",
    type: 'switch',
    span: 12,
    activeValue: true,
    inactiveValue: false,
    activeText: "启用",
    inactiveText: "禁用"
}
```

**特有属性：**
- `activeValue`: 激活时的值
- `inactiveValue`: 非激活时的值
- `activeText`: 激活时的文本
- `inactiveText`: 非激活时的文本

### 11. 日期选择器 (date)

用于日期选择的组件。

```javascript
{
    label: "出生日期",
    prop: "birthday",
    type: 'date',
    span: 12,
    format: 'YYYY-MM-DD',
    valueFormat: 'YYYY-MM-DD',
    clearable: true
}
```

**特有属性：**
- `format`: 显示格式
- `valueFormat`: 值格式

### 12. 日期时间选择器 (datetime)

用于日期和时间选择的组件。

```javascript
{
    label: "创建时间",
    prop: "createTime",
    type: 'datetime',
    span: 12,
    format: 'YYYY-MM-DD HH:mm:ss',
    valueFormat: 'YYYY-MM-DD HH:mm:ss'
}
```

### 13. 时间选择器 (time)

专门用于时间选择的组件。

```javascript
{
    label: "工作时间",
    prop: "workTime",
    type: 'time',
    span: 12,
    format: 'HH:mm:ss',
    valueFormat: 'HH:mm:ss'
}
```

### 14. 日期范围选择器 (daterange)

用于选择日期范围的组件。

```javascript
{
    label: "有效期",
    prop: "validPeriod",
    type: 'daterange',
    span: 12,
    format: 'YYYY-MM-DD',
    valueFormat: 'YYYY-MM-DD'
}
```

### 15. 颜色选择器 (color)

用于颜色选择的组件。

```javascript
{
    label: "主题色",
    prop: "themeColor",
    type: 'color',
    span: 12,
    showAlpha: true
}
```

**特有属性：**
- `showAlpha`: 是否支持透明度选择

### 16. 评分 (rate)

用于评分的星级组件。

```javascript
{
    label: "评分",
    prop: "rating",
    type: 'rate',
    span: 12,
    max: 5,
    allowHalf: true,
    defaultValue: 0
}
```

**特有属性：**
- `max`: 最大分值
- `allowHalf`: 是否允许半选

### 17. 级联选择器 (cascader)

用于级联数据选择的组件。

```javascript
{
    label: "地区",
    prop: "region",
    type: 'cascader',
    span: 12,
    dicData: regionOptions,
    props: {
        checkStrictly: false,
        expandTrigger: 'click'
    },
    filterable: true,
    clearable: true
}
```

**特有属性：**
- `props`: 级联器配置选项
- `expandTrigger`: 次级菜单的展开方式

## 高级功能

### 自定义插槽

支持在表单和表格中使用自定义插槽。

```javascript
{
    label: "自定义内容",
    prop: "customField",
    formSlot: true,     // 使用表单插槽
    tableSlot: true,    // 使用表格插槽
    span: 24
}
```

### 分隔线

在表单中添加分隔线，用于分组显示。

```javascript
{
    divider: true,
    label: "基础信息",
    prop: "divider1"
}
```

### 字段联动控制

通过 `control` 函数实现字段间的联动控制。

```javascript
{
    label: "是否展示扩展信息",
    prop: "showExtendInfo",
    type: 'select',
    dicData: sys_yes_no,
    control: (val, formData) => {
        if (val !== "Y") {
            return {
                extendInfo: {
                    viewDisplay: false,
                    addDisplay: false,
                    editDisplay: false
                }
            }
        } else {
            return {
                extendInfo: {
                    viewDisplay: true,
                    addDisplay: true,
                    editDisplay: true
                }
            }
        }
    }
}
```

## 数据源配置

### 字典数据

```javascript
// 从后端字典获取
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

// 静态数据
const statusOptions = [
    { label: "启用", value: "0" },
    { label: "禁用", value: "1" }
];
```

### 树形数据

```javascript
const treeOptions = [
    {
        id: '1',
        value: '1',
        label: '一级 1',
        children: [
            {
                id: '1-1',
                value: '1-1',
                label: '二级 1-1',
                children: [
                    { id: '1-1-1', value: '1-1-1', label: '三级 1-1-1' }
                ]
            }
        ]
    }
];
```

## 使用示例

### 1. 完整的用户管理配置

```javascript
export const createUserTableOption = (proxy) => {
    const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex");
    
    return {
        dialogWidth: '800px',
        labelWidth: '100px',
        column: [
            {
                label: "用户名",
                prop: "userName",
                type: 'input',
                span: 12,
                search: true,
                rules: [{ required: true, message: "请输入用户名", trigger: "blur" }]
            },
            {
                label: "性别",
                prop: "sex",
                type: 'radio',
                span: 12,
                dicData: sys_user_sex
            },
            {
                label: "状态",
                prop: "status",
                type: 'select',
                span: 12,
                search: true,
                dicData: sys_normal_disable
            }
        ]
    };
};
```

### 2. 在组件中使用

```vue
<template>
    <TableList
        :columns="tableColumns"
        :data="tableData"
        :loading="loading"
        show-selection
        show-operation
        @search="handleSearch"
    />
    
    <FormDialog
        ref="formDialogRef"
        :form-option="formOption"
        :form-fields="formFields"
        @submit="handleFormSubmit"
    />
</template>

<script setup>
import { createUserTableOption } from '@/const/vue-template';

const proxy = getCurrentInstance().proxy;

// 初始化配置
const baseOption = createUserTableOption(proxy);
const { tableColumns, formFields, formOption } = extractTableColumns(baseOption);
</script>
```

## 注意事项

1. **字段属性名（prop）** 必须与后端数据模型保持一致
2. **字典数据** 需要确保格式正确，包含 `label` 和 `value` 属性
3. **验证规则** 遵循 Element Plus 的表单验证规范
4. **联动控制** 函数需要返回正确的配置对象结构
5. **栅格布局** span 值总和建议不超过 24
6. **日期格式** 需要与后端接口约定保持一致

## 扩展开发

如需添加新的字段类型，需要在以下位置进行扩展：

1. `FormFieldComponent.vue` - 添加新组件的渲染逻辑
2. `extractTableColumns.js` - 添加字段类型处理
3. 本配置文件 - 添加示例配置

通过这种配置化的方式，可以快速构建功能完整、用户体验良好的管理界面。
