<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="菜单名称" prop="menuName">
            <el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable style="width: 200px"
               @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="菜单状态" clearable style="width: 200px">
               <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" class="custom-btn" @click="handleQuery">搜 索</el-button>
            <el-button class="custom-btn" @click="resetQuery">重 置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" class="custom-btn" plain @click="handleAdd"
               v-hasPermi="['system:menu:add']">新 增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="info" class="custom-btn" plain @click="toggleExpandAll">展开/折叠</el-button>
         </el-col>
      </el-row>

      <el-table v-if="refreshTable" v-loading="loading" :data="menuList" row-key="menuId"
         :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
         <el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="160"></el-table-column>
         <el-table-column prop="icon" label="图标" align="center" width="100">
            <template #default="scope">
               <svg-icon :icon-class="scope.row.icon" />
            </template>
         </el-table-column>
         <el-table-column prop="orderNum" label="排序" width="60"></el-table-column>
         <el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" width="160" prop="createTime">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" @click="handleView(scope.row)"
                  v-hasPermi="['system:menu:query']">查看</el-button>
               <el-button link type="primary" @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:menu:edit']">修改</el-button>
               <el-button link type="primary" @click="handleAdd(scope.row)"
                  v-hasPermi="['system:menu:add']">新增</el-button>
               <el-button link type="danger" @click="handleDelete(scope.row)"
                  v-hasPermi="['system:menu:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 表单弹窗 -->
      <MenuFormDialog ref="menuFormDialogRef" :parentMenuId="currentParentMenuId" @submit="handleDialogSubmit"
         @cancel="handleDialogCancel" />
   </div>
</template>

<script setup name="Menu">
import { addMenu, delMenu, getMenu, listMenu, updateMenu } from "@/api/system/menu"
import SvgIcon from "@/components/SvgIcon"
import IconSelect from "@/components/IconSelect"
import MenuFormDialog from "./MenuFormDialog.vue"

const { proxy } = getCurrentInstance()
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable")

const menuList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const isExpandAll = ref(false)
const refreshTable = ref(true)

// 弹窗相关
const menuFormDialogRef = ref()
const currentParentMenuId = ref(0)

const data = reactive({
   queryParams: {
      menuName: undefined,
      visible: undefined
   }
})

const { queryParams } = toRefs(data)

/** 查询菜单列表 */
function getList() {
   loading.value = true
   listMenu(queryParams.value).then(response => {
      menuList.value = proxy.handleTree(response.data, "menuId")
      loading.value = false
   })
}



/** 搜索按钮操作 */
function handleQuery() {
   getList()
}

/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef")
   handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
   currentParentMenuId.value = row?.menuId || 0
   menuFormDialogRef.value?.openDialog('add', "新增菜单")
}

/** 展开/折叠操作 */
function toggleExpandAll() {
   refreshTable.value = false
   isExpandAll.value = !isExpandAll.value
   nextTick(() => {
      refreshTable.value = true
   })
}

/** 修改按钮操作 */
function handleUpdate(row) {
   menuFormDialogRef.value?.openDialog('edit', "修改菜单", row)
}

/** 查看按钮操作 */
function handleView(row) {
   menuFormDialogRef.value?.openDialog('view', "查看菜单", row)
}

/** 弹窗提交回调 */
async function handleDialogSubmit({ type, data }) {
   try {
      if (type === 'add') {
         await addMenu(data)
         proxy.$modal.msgSuccess("新增成功")
      } else if (type === 'edit') {
         await updateMenu(data)
         proxy.$modal.msgSuccess("修改成功")
      }

      menuFormDialogRef.value?.onSubmitSuccess()
      getList()
   } catch (error) {
      console.error('提交失败:', error)
      proxy.$modal.msgError('操作失败')
      menuFormDialogRef.value?.onSubmitError()
   }
}

/** 弹窗取消回调 */
function handleDialogCancel() {
   currentParentMenuId.value = 0
}

/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm('是否确认删除名称为"' + row.menuName + '"的数据项?').then(function () {
      return delMenu(row.menuId)
   }).then(() => {
      getList()
      proxy.$modal.msgSuccess("删除成功")
   }).catch(() => { })
}

getList()
</script>

<style lang="scss" scoped>
.custom-btn {
   margin-left: 12px;
}
</style>
