<template>
  <div class="training-application-container app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>培训申请平台</h1>
      <p>欢迎使用培训申请平台，您可以在这里查看和申请各类培训课程</p>
    </div>

    <!-- 培训申请列表 -->
    <div class="training-list">
      <div class="list-header">
        <h2>可申请培训列表</h2>
        <div class="list-stats" v-if="trainingList.length > 0">
          <span class="stats-item">
            <i class="el-icon-document"></i>
            共 {{ trainingList.length }} 个培训项目
          </span>
        </div>
      </div>
      <div class="training-items" v-loading="loading">
        <!-- 空状态 -->
        <div v-if="!loading && trainingList.length === 0" class="empty-state">
          <div class="empty-content">
            <div class="empty-icon">
              <el-icon size="80"><Document /></el-icon>
            </div>
            <h3>暂无培训项目</h3>
            <p>当前没有可申请的培训项目，请稍后再来查看</p>
          </div>
        </div>

        <div v-for="training in trainingList" :key="training.orderId" class="training-item" :class="{
          'has-application': training.userApplication,
          'application-approved': training.applicationStatus === '1',
          'application-rejected': training.applicationStatus === '2',
          'application-pending': training.applicationStatus === '0',
          'application-cancelled': training.applicationStatus === '3'
        }">
          <div class="training-content">
            <div class="training-header">
              <h3 class="training-title">{{ training.orderTitle }}</h3>
              <el-tag :type="getTypeTagType(training.trainingType)" size="small">
                {{ training.trainingType }}
              </el-tag>
            </div>

            <p class="training-description">{{ training.orderDescription }}</p>

            <div class="training-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">培训级别：</span>
                  <span class="value">{{ training.trainingLevel }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训时长：</span>
                  <span class="value">{{ training.trainingDuration }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训费用：</span>
                  <span class="value price">{{ training.trainingFee ? '￥' + training.trainingFee : '免费' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <span class="label">培训时间：</span>
                  <span class="value">{{ formatDate(training.startDate) }} 至 {{ formatDate(training.endDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">培训地址：</span>
                  <span class="value">{{ training.trainingAddress }}</span>
                </div>
                <div class="info-item">
                  <span class="label">报名人数：</span>
                  <span class="value">{{ training.currentParticipants || 0 }}/{{ training.maxParticipants || 0
                    }}人</span>
                </div>
              </div>

              <div class="info-row" v-if="training.userApplication">
                <div class="info-item">
                  <span class="label">申请状态：</span>
                  <el-tag :type="getApplicationStatusTagType(training.applicationStatus)" size="small">
                    {{ getApplicationStatusText(training.applicationStatus) }}
                  </el-tag>
                </div>
                <div class="info-item">
                  <span class="label">申请时间：</span>
                  <span class="value">{{ formatDate(training.userApplication.applicationTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="training-actions">
            <!-- 未申请过或申请被拒绝，可以申请 -->
            <el-button v-if="training.canApply" type="primary" size="default" @click="handleApply(training)"
              :disabled="!canApplyTraining(training)" class="action-btn primary-btn">
              <el-icon>
                <Plus />
              </el-icon>
              {{ training.userApplication ? '重新申请' : '立即申请' }}
            </el-button>

            <!-- 已申请，显示申请状态 -->
            <el-button v-else-if="training.userApplication" :type="getApplicationButtonType(training.applicationStatus)"
              size="default" disabled class="status-btn">
              <el-icon>
                <InfoFilled />
              </el-icon>
              {{ getApplicationStatusText(training.applicationStatus) }}
            </el-button>

            <!-- 查看申请信息 -->
            <el-button v-if="training.userApplication" type="success" size="default"
              @click="handleViewApplication(training.userApplication)" class="action-btn view-btn">
              <el-icon>
                <View />
              </el-icon>
              查看申请
            </el-button>

            <!-- 取消申请 -->
            <el-button v-if="training.userApplication && training.applicationStatus === '0'" type="warning"
              size="default" @click="handleCancelApplication(training.userApplication)" class="action-btn cancel-btn">
              <el-icon>
                <Close />
              </el-icon>
              取消申请
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请弹窗 -->
    <el-dialog v-model="applyDialogVisible" :title="`申请 - ${currentTraining?.orderTitle}`" width="800px"
      :close-on-click-modal="false" append-to-body>
      <div class="apply-form">
        <el-form ref="applyFormRef" :model="applyForm" :rules="applyRules" label-width="120px">
          <el-form-item label="申请培训" prop="orderId">
            <el-input v-model="currentTraining.orderTitle" disabled />
          </el-form-item>

          <el-form-item label="申请人姓名" prop="applicantName">
            <el-input v-model="applyForm.applicantName" placeholder="请输入申请人姓名" />
          </el-form-item>

          <el-form-item label="联系电话" prop="applicantPhone">
            <el-input v-model="applyForm.applicantPhone" placeholder="请输入联系电话" />
          </el-form-item>

          <el-form-item label="邮箱地址" prop="applicantEmail">
            <el-input v-model="applyForm.applicantEmail" placeholder="请输入邮箱地址" />
          </el-form-item>

          <el-form-item label="性别" prop="applicantGender">
            <el-select v-model="applyForm.applicantGender" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="年龄" prop="applicantAge">
            <el-input-number v-model="applyForm.applicantAge" :min="16" :max="100" placeholder="请输入年龄"
              style="width: 100%" />
          </el-form-item>

          <el-form-item label="学历" prop="applicantEducation">
            <el-select v-model="applyForm.applicantEducation" placeholder="请选择学历" style="width: 100%">
              <el-option label="小学" value="小学"></el-option>
              <el-option label="初中" value="初中"></el-option>
              <el-option label="中专" value="中专"></el-option>
              <el-option label="高中" value="高中"></el-option>
              <el-option label="大专" value="大专"></el-option>
              <el-option label="本科" value="本科"></el-option>
              <el-option label="硕士" value="硕士"></el-option>
              <el-option label="博士" value="博士"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="身份证号" prop="applicantIdCard">
            <el-input v-model="applyForm.applicantIdCard" placeholder="请输入身份证号" />
          </el-form-item>

          <el-form-item label="联系地址" prop="applicantAddress">
            <el-input v-model="applyForm.applicantAddress" placeholder="请输入联系地址" />
          </el-form-item>

          <el-form-item label="工作经验" prop="applicantExperience">
            <el-input v-model="applyForm.applicantExperience" type="textarea" :rows="3" placeholder="请简要描述您的工作经验" />
          </el-form-item>

          <el-form-item label="申请备注" prop="applicationNote">
            <el-input v-model="applyForm.applicationNote" type="textarea" :rows="3" placeholder="请输入申请备注（选填）" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="applyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmitApply">
            提交申请
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 申请详情弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="申请详情" width="600px" append-to-body>
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人姓名">{{ currentApplication.applicantName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentApplication.applicantPhone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱地址">{{ currentApplication.applicantEmail }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentApplication.applicantGender }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentApplication.applicantAge }}</el-descriptions-item>
          <el-descriptions-item label="学历">{{ currentApplication.applicantEducation }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ currentApplication.applicantIdCard }}</el-descriptions-item>
          <el-descriptions-item label="联系地址">{{ currentApplication.applicantAddress }}</el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getApplicationStatusTagType(currentApplication.applicationStatus)">
              {{ getApplicationStatusText(currentApplication.applicationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(currentApplication.applicationTime) }}</el-descriptions-item>
          <el-descriptions-item label="工作经验" :span="2">{{ currentApplication.applicantExperience || '--'
            }}</el-descriptions-item>
          <el-descriptions-item label="申请备注" :span="2">{{ currentApplication.applicationNote || '--'
            }}</el-descriptions-item>
          <el-descriptions-item v-if="currentApplication.reviewComment" label="审核意见" :span="2">{{
            currentApplication.reviewComment }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, InfoFilled, View, Close, Document } from '@element-plus/icons-vue'
import { listTrainingOrder } from '@/api/training/order'
import { submitTrainingApplication, getMyApplications, cancelMyApplication } from '@/api/training/application'

// 响应式数据
const loading = ref(false)
const trainingList = ref([])
const applyDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitLoading = ref(false)
const currentTraining = ref(null)
const currentApplication = ref(null)
const applyFormRef = ref(null)

// 申请表单数据
const applyForm = reactive({
  orderId: null,
  applicantName: '',
  applicantPhone: '',
  applicantEmail: '',
  applicantIdCard: '',
  applicantGender: '',
  applicantAge: null,
  applicantEducation: '',
  applicantExperience: '',
  applicantAddress: '',
  applicationNote: ''
})

// 申请表单验证规则
const applyRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  applicantEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  applicantIdCard: [
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  loadTrainingList()
})

// 方法
const loadTrainingList = async () => {
  loading.value = true
  try {
    // 获取所有已发布的培训订单
    const trainingResponse = await listTrainingOrder({ orderStatus: '1' })
    const trainings = trainingResponse.rows || []

    // 获取当前用户的申请记录
    const applicationResponse = await getMyApplications()
    const applications = applicationResponse.data || []

    // 合并培训信息和申请状态
    trainingList.value = trainings.map(training => {
      const userApplication = applications.find(app => app.orderId === training.orderId)
      return {
        ...training,
        userApplication,
        applicationStatus: userApplication?.applicationStatus,
        canApply: !userApplication || userApplication.applicationStatus === '2' || userApplication.applicationStatus === '3'
      }
    })
  } catch (error) {
    ElMessage.error('获取培训列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleApply = (training) => {
  currentTraining.value = training
  applyForm.orderId = training.orderId

  // 如果是重新申请，填充之前的信息
  if (training.userApplication) {
    Object.assign(applyForm, {
      applicantName: training.userApplication.applicantName,
      applicantPhone: training.userApplication.applicantPhone,
      applicantEmail: training.userApplication.applicantEmail,
      applicantIdCard: training.userApplication.applicantIdCard,
      applicantGender: training.userApplication.applicantGender,
      applicantAge: training.userApplication.applicantAge,
      applicantEducation: training.userApplication.applicantEducation,
      applicantExperience: training.userApplication.applicantExperience,
      applicantAddress: training.userApplication.applicantAddress,
      applicationNote: ''
    })
  } else {
    // 重置表单
    Object.assign(applyForm, {
      orderId: training.orderId,
      applicantName: '',
      applicantPhone: '',
      applicantEmail: '',
      applicantIdCard: '',
      applicantGender: '',
      applicantAge: null,
      applicantEducation: '',
      applicantExperience: '',
      applicantAddress: '',
      applicationNote: ''
    })
  }

  applyDialogVisible.value = true
}

const handleSubmitApply = () => {
  if (!applyFormRef.value) return

  applyFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true

      try {
        await submitTrainingApplication(applyForm)
        ElMessage.success('申请提交成功，请等待审核')
        applyDialogVisible.value = false

        // 重新加载培训列表
        await loadTrainingList()
      } catch (error) {
        ElMessage.error(error.msg || '申请提交失败，请稍后重试')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleViewApplication = (application) => {
  currentApplication.value = application
  viewDialogVisible.value = true
}

const handleCancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确认要取消申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyApplication(application.applicationId)
    ElMessage.success('取消申请成功')

    // 重新加载培训列表
    await loadTrainingList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消申请失败')
    }
  }
}

const canApplyTraining = (training) => {
  // 检查是否可以申请
  const now = new Date()
  const registrationDeadline = training.registrationDeadline ? new Date(training.registrationDeadline) : null

  // 检查报名截止时间
  if (registrationDeadline && now > registrationDeadline) {
    return false
  }

  // 检查报名人数是否已满
  const current = training.currentParticipants || 0
  const max = training.maxParticipants || 0
  if (current >= max && max > 0) {
    return false
  }

  return true
}

const getTypeTagType = (trainingType) => {
  const typeMap = {
    '技能培训': 'primary',
    '管理培训': 'success',
    '安全培训': 'warning',
    '专业培训': 'info'
  }
  return typeMap[trainingType] || 'default'
}

const getApplicationStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

const getApplicationButtonType = (status) => {
  const typeMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return typeMap[status] || 'info'
}

const formatDate = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.training-application-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

.page-header {
  text-align: center;
  padding: 60px 20px 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 0;

  h1 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 36px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
  }

  p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.training-list {
  padding: 40px 20px;
  max-width: 1400px;
  margin: 0 auto;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;

    h2 {
      color: #ffffff;
      font-size: 28px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      margin: 0;
    }

    .list-stats {
      .stats-item {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        backdrop-filter: blur(10px);

        i {
          font-size: 18px;
        }
      }
    }
  }
}

.training-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(650px, 1fr));
  gap: 30px;
  padding: 20px 0;

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;

    .empty-content {
      text-align: center;
      color: rgba(255, 255, 255, 0.8);

      .empty-icon {
        margin-bottom: 20px;
        opacity: 0.6;

        .el-icon {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      h3 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
        color: rgba(255, 255, 255, 0.9);
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
      }
    }
  }
}

.training-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.4);

    &::before {
      transform: scaleX(1);
    }
  }

  &.has-application::before {
    background: linear-gradient(90deg, #409eff, #36cfc9);
    transform: scaleX(1);
  }

  &.application-approved::before {
    background: linear-gradient(90deg, #67c23a, #95de64);
    transform: scaleX(1);
  }

  &.application-rejected::before {
    background: linear-gradient(90deg, #f56c6c, #ff7875);
    transform: scaleX(1);
  }

  &.application-pending::before {
    background: linear-gradient(90deg, #e6a23c, #ffc53d);
    transform: scaleX(1);
  }

  &.application-cancelled::before {
    background: linear-gradient(90deg, #909399, #bfbfbf);
    transform: scaleX(1);
  }
}

.training-content {
  flex: 1;
}

.training-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .training-title {
    color: #1a1a1a;
    font-size: 22px;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.training-description {
  color: #4a5568;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 15px;
}

.training-info {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;

  .info-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-item {
      flex: 1;
      min-width: 180px;
      display: flex;
      align-items: center;

      .label {
        font-weight: 600;
        color: #4a5568;
        margin-right: 8px;
        font-size: 14px;
        white-space: nowrap;
      }

      .value {
        color: #2d3748;
        font-weight: 500;
        font-size: 14px;

        &.price {
          color: #e53e3e;
          font-weight: 700;
          font-size: 16px;
        }
      }
    }
  }
}

.training-actions {
  margin-top: 25px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);

  .action-btn {
    flex: 1;
    min-width: 120px;
    height: 42px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;

    &.primary-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }
    }

    &.view-btn {
      background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
      }
    }

    &.cancel-btn {
      background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
      }
    }
  }

  .status-btn {
    flex: 1;
    min-width: 120px;
    height: 42px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    opacity: 0.8;
    cursor: not-allowed;
  }
}

.apply-form {
  max-height: 60vh;
  overflow-y: auto;
  padding: 10px;

  :deep(.el-form-item__label) {
    font-weight: 600;
    color: #4a5568;
  }

  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-select .el-input__wrapper) {
    border-radius: 8px;
  }

  :deep(.el-textarea__inner) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.application-detail {
  .el-descriptions {
    margin-top: 20px;

    :deep(.el-descriptions__header) {
      margin-bottom: 20px;
    }

    :deep(.el-descriptions-item__label) {
      font-weight: 600;
      color: #4a5568;
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  .el-button {
    border-radius: 8px;
    font-weight: 600;
    padding: 12px 24px;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .training-items {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .training-list {
    padding: 30px 15px;
  }
}

@media (max-width: 768px) {
  .training-application-container {
    min-height: 100vh;
  }

  .page-header {
    padding: 40px 15px 30px;

    h1 {
      font-size: 28px;
    }

    p {
      font-size: 16px;
    }
  }

  .training-list {
    padding: 20px 10px;

    h2 {
      font-size: 24px;
    }
  }

  .training-items {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 10px 0;
  }

  .training-item {
    padding: 20px;
    border-radius: 12px;

    .training-header .training-title {
      font-size: 18px;
    }
  }

  .training-info {
    padding: 15px;

    .info-row {
      gap: 15px;

      .info-item {
        min-width: 140px;

        .label {
          font-size: 13px;
        }

        .value {
          font-size: 13px;

          &.price {
            font-size: 15px;
          }
        }
      }
    }
  }

  .training-actions {
    gap: 8px;

    .action-btn,
    .status-btn {
      min-width: 100px;
      height: 38px;
      font-size: 13px;
    }
  }

  .apply-form {
    padding: 5px;
  }
}

@media (max-width: 480px) {
  .page-header {
    h1 {
      font-size: 24px;
    }

    p {
      font-size: 14px;
    }
  }

  .training-info .info-row {
    flex-direction: column;
    gap: 10px;

    .info-item {
      min-width: auto;
      justify-content: space-between;
    }
  }

  .training-actions {
    flex-direction: column;

    .action-btn,
    .status-btn {
      min-width: auto;
      width: 100%;
    }
  }
}

// 全局弹窗样式优化
:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-weight: 700;
      font-size: 18px;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    background: #f8fafc;
  }
}

// 标签样式优化
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 600;
  padding: 4px 12px;
  border: none;

  &.el-tag--primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  &.el-tag--success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
  }

  &.el-tag--warning {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
  }

  &.el-tag--danger {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
  }

  &.el-tag--info {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
  }
}

// 加载动画优化
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}
</style>
