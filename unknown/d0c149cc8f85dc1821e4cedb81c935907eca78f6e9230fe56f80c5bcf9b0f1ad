package com.sux.web.controller.training;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.TrainingApplication;
import com.sux.system.service.ITrainingApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训报名Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/training/application")
public class TrainingApplicationController extends BaseController {
    @Autowired
    private ITrainingApplicationService trainingApplicationService;

    /**
     * 查询培训报名列表
     */
    @PreAuthorize("@ss.hasPermi('training:application:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrainingApplication trainingApplication) {
        startPage();
        List<TrainingApplication> list = trainingApplicationService.selectTrainingApplicationList(trainingApplication);
        return getDataTable(list);
    }

    /**
     * 导出培训报名列表
     */
    @PreAuthorize("@ss.hasPermi('training:application:export')")
    @Log(title = "培训报名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrainingApplication trainingApplication) {
        List<TrainingApplication> list = trainingApplicationService.selectTrainingApplicationList(trainingApplication);
        ExcelUtil<TrainingApplication> util = new ExcelUtil<TrainingApplication>(TrainingApplication.class);
        util.exportExcel(response, list, "培训报名数据");
    }

    /**
     * 获取培训报名详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:application:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId) {
        return success(trainingApplicationService.selectTrainingApplicationByApplicationId(applicationId));
    }

    /**
     * 新增培训报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:add')")
    @Log(title = "培训报名", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TrainingApplication trainingApplication) {
        if (!trainingApplicationService.checkApplicationUnique(trainingApplication)) {
            return error("新增培训报名失败，该手机号或用户已报名此培训");
        }
        return toAjax(trainingApplicationService.insertTrainingApplication(trainingApplication));
    }

    /**
     * 修改培训报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:edit')")
    @Log(title = "培训报名", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TrainingApplication trainingApplication) {
        if (!trainingApplicationService.checkApplicationUnique(trainingApplication)) {
            return error("修改培训报名失败，该手机号或用户已报名此培训");
        }
        return toAjax(trainingApplicationService.updateTrainingApplication(trainingApplication));
    }

    /**
     * 删除培训报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:remove')")
    @Log(title = "培训报名", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds) {
        return toAjax(trainingApplicationService.deleteTrainingApplicationByApplicationIds(applicationIds));
    }

    /**
     * 审核培训报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:review')")
    @Log(title = "培训报名", businessType = BusinessType.UPDATE)
    @PutMapping("/review/{applicationId}")
    public AjaxResult review(@PathVariable Long applicationId, 
                           @RequestParam String status, 
                           @RequestParam(required = false) String reviewComment) {
        String reviewer = getUsername();
        return toAjax(trainingApplicationService.reviewApplication(applicationId, status, reviewer, reviewComment));
    }

    /**
     * 批量审核培训报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:review')")
    @Log(title = "培训报名", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-review")
    public AjaxResult batchReview(@RequestParam Long[] applicationIds, 
                                @RequestParam String status, 
                                @RequestParam(required = false) String reviewComment) {
        String reviewer = getUsername();
        int successCount = 0;
        for (Long applicationId : applicationIds) {
            int result = trainingApplicationService.reviewApplication(applicationId, status, reviewer, reviewComment);
            if (result > 0) {
                successCount++;
            }
        }
        return success("成功审核 " + successCount + " 条报名记录");
    }

    /**
     * 取消报名
     */
    @PreAuthorize("@ss.hasPermi('training:application:cancel')")
    @Log(title = "培训报名", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{applicationId}")
    public AjaxResult cancel(@PathVariable Long applicationId) {
        return toAjax(trainingApplicationService.cancelApplication(applicationId));
    }

    /**
     * 获取某个培训订单的报名列表
     */
    @PreAuthorize("@ss.hasPermi('training:application:list')")
    @GetMapping("/order/{orderId}")
    public AjaxResult getApplicationsByOrderId(@PathVariable Long orderId) {
        List<TrainingApplication> list = trainingApplicationService.getApplicationsByOrderId(orderId);
        return success(list);
    }

    /**
     * 统计某个培训订单的报名人数
     */
    @PreAuthorize("@ss.hasPermi('training:application:list')")
    @GetMapping("/count/{orderId}")
    public AjaxResult countApplicationsByOrderId(@PathVariable Long orderId) {
        int count = trainingApplicationService.countApplicationsByOrderId(orderId);
        return success(count);
    }
}
