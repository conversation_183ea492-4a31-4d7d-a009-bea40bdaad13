package com.sux.web.controller.policy;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.PolicyApplication;
import com.sux.system.domain.PolicyApprovalRecord;
import com.sux.system.service.IPolicyApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 政策申请Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/policy/application")
public class PolicyApplicationController extends BaseController {
    @Autowired
    private IPolicyApplicationService policyApplicationService;

    /**
     * 查询政策申请列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PolicyApplication policyApplication) {
        startPage();
        List<PolicyApplication> list = policyApplicationService.selectPolicyApplicationList(policyApplication);
        return getDataTable(list);
    }

    /**
     * 查询待初审申请列表
     */
    @GetMapping("/pending-first-review")
    public TableDataInfo pendingFirstReview(PolicyApplication policyApplication) {
        startPage();
        List<PolicyApplication> list = policyApplicationService.selectPendingFirstReviewList(policyApplication);
        return getDataTable(list);
    }

    /**
     * 查询待终审申请列表
     */
    @GetMapping("/pending-final-review")
    public TableDataInfo pendingFinalReview(PolicyApplication policyApplication) {
        startPage();
        List<PolicyApplication> list = policyApplicationService.selectPendingFinalReviewList(policyApplication);
        return getDataTable(list);
    }

    /**
     * 查询我的申请列表
     */
    @GetMapping("/my-applications")
    public TableDataInfo myApplications(PolicyApplication policyApplication) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<PolicyApplication> list = policyApplicationService.selectMyApplicationsList(policyApplication, userId);
        return getDataTable(list);
    }

    /**
     * 查询所有申请列表（管理员权限）
     */
    @GetMapping("/all")
    public TableDataInfo allApplications(PolicyApplication policyApplication) {
        startPage();
        List<PolicyApplication> list = policyApplicationService.selectAllApplicationsList(policyApplication);
        return getDataTable(list);
    }

    /**
     * 导出政策申请列表
     */
    @Log(title = "政策申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyApplication policyApplication) {
        List<PolicyApplication> list = policyApplicationService.selectPolicyApplicationList(policyApplication);
        ExcelUtil<PolicyApplication> util = new ExcelUtil<PolicyApplication>(PolicyApplication.class);
        util.exportExcel(response, list, "政策申请数据");
    }

    /**
     * 获取政策申请详细信息
     */
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId) {
        return success(policyApplicationService.selectPolicyApplicationByApplicationId(applicationId));
    }

    /**
     * 新增政策申请
     */
    @Log(title = "政策申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody PolicyApplication policyApplication) {
        return toAjax(policyApplicationService.insertPolicyApplication(policyApplication));
    }

    /**
     * 修改政策申请
     */
    @PreAuthorize("@ss.hasPermi('policy:application:edit')")
    @Log(title = "政策申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody PolicyApplication policyApplication) {
        return toAjax(policyApplicationService.updatePolicyApplication(policyApplication));
    }

    /**
     * 删除政策申请
     */
    @PreAuthorize("@ss.hasPermi('policy:application:remove')")
    @Log(title = "政策申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds) {
        return toAjax(policyApplicationService.deletePolicyApplicationByApplicationIds(applicationIds));
    }

    /**
     * 初审操作
     */
    @PreAuthorize("@ss.hasPermi('policy:application:first-review')")
    @Log(title = "政策申请初审", businessType = BusinessType.UPDATE)
    @PostMapping("/first-review")
    public AjaxResult firstReview(@RequestBody Map<String, Object> params) {
        Long applicationId = Long.valueOf(params.get("applicationId").toString());
        String approvalStatus = params.get("approvalStatus").toString();
        String approvalComment = params.get("approvalComment").toString();
        String approvalFiles = params.get("approvalFiles") != null ? params.get("approvalFiles").toString() : null;

        int result = policyApplicationService.firstReview(applicationId, approvalStatus, approvalComment, approvalFiles);

        if (result > 0) {
            String statusText = "1".equals(approvalStatus) ? "通过" : "拒绝";
            return success("初审" + statusText + "成功");
        } else {
            return error("初审操作失败");
        }
    }

    /**
     * 终审操作
     */
    @PreAuthorize("@ss.hasPermi('policy:application:final-review')")
    @Log(title = "政策申请终审", businessType = BusinessType.UPDATE)
    @PostMapping("/final-review")
    public AjaxResult finalReview(@RequestBody Map<String, Object> params) {
        Long applicationId = Long.valueOf(params.get("applicationId").toString());
        String approvalStatus = params.get("approvalStatus").toString();
        String approvalComment = params.get("approvalComment").toString();
        String approvalFiles = params.get("approvalFiles") != null ? params.get("approvalFiles").toString() : null;

        int result = policyApplicationService.finalReview(applicationId, approvalStatus, approvalComment, approvalFiles);

        if (result > 0) {
            String statusText = "1".equals(approvalStatus) ? "通过" : "拒绝";
            return success("终审" + statusText + "成功");
        } else {
            return error("终审操作失败");
        }
    }

    /**
     * 查询审批记录
     */
    @GetMapping("/approval-records/{applicationId}")
    public AjaxResult getApprovalRecords(@PathVariable("applicationId") Long applicationId) {
        List<PolicyApprovalRecord> records = policyApplicationService.getApprovalRecords(applicationId);
        return success(records);
    }
}
