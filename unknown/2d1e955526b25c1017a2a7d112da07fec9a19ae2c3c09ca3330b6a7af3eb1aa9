<template>
  <div class="view-content">
    <el-row :gutter="24" class="view-row">
      <template v-for="(field, index) in visibleFields" :key="index">
        <!-- 处理分隔线 -->
        <el-col :span="24" v-if="field.divider" class="view-divider">
          <slot :name="field.prop" :field="field" v-if="shouldShowDivider(index)">
            <div class="divider-wrapper">
              <div class="divider-title">{{ field.label }}</div>
              <div class="divider-line"></div>
            </div>
          </slot>
        </el-col>

        <!-- 处理普通字段 -->
        <el-col v-else :span="field.span || 12" class="view-item" :class="{ 'full-width-item': field.span === 24 }">
          <div class="view-item-content">
            <div class="view-label" :style="{ width: props.labelWidth }">{{ field.label }}：</div>
            <div class="view-value">
              <!-- 使用命名插槽优先展示自定义内容 -->
              <slot :name="field.prop" :value="modelValue[field.prop]" :row="modelValue" :field="field">
                <!-- 字典值显示 -->
                <template v-if="field.dicData">
                  {{ getDictLabel(field.dicData, modelValue[field.prop]) }}
                </template>
                <!-- 日期类型 -->
                <template v-else-if="field.type === 'date'">
                  {{ parseTime(modelValue[field.prop], '{y}-{m}-{d}') || emptyText }}
                </template>
                <!-- 日期时间类型 -->
                <template v-else-if="field.type === 'datetime'">
                  {{ parseTime(modelValue[field.prop], '{y}-{m}-{d} {h}:{i}:{s}') || emptyText }}
                </template>
                <!-- 多行文本 -->
                <template v-else-if="field.type === 'textarea' && modelValue[field.prop]">
                  <div class="multiline-text">{{ modelValue[field.prop] }}</div>
                </template>
                <!-- 插槽类型 -->
                <template v-else-if="field.viewSlot">
                  <slot :name="`${field.prop}-default`" :row="modelValue" :value="modelValue[field.prop]">
                    {{ modelValue[field.prop] || emptyText }}
                  </slot>
                </template>
                <!-- 默认文本显示 -->
                <template v-else>
                  {{ modelValue[field.prop] || emptyText }}
                </template>
              </slot>
            </div>
          </div>
        </el-col>
      </template>
    </el-row>
    <!-- 自定义附加内容插槽 -->
    <slot name="append"></slot>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import { parseTime } from '@/utils/ruoyi';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  fields: {
    type: Array,
    default: () => []
  },
  labelWidth: {
    type: String,
    default: '120px'
  },
  emptyText: {
    type: String,
    default: '--'
  }
});

// 控制显示字段状态
const fieldState = ref({});

// 监听表单数据变化，重新应用联动规则
watch(() => props.modelValue, (val) => {
  applyControlRules();
}, { deep: true });

// 初始化时应用联动规则
onMounted(() => {
  applyControlRules();
});

// 过滤处理字段，只显示应该在查看模式下展示的字段
const processedFields = computed(() => {
  return props.fields.filter(field => field.viewDisplay !== false);
});

// 应用表单联动控制规则
const applyControlRules = () => {
  // 先初始化所有字段的状态为默认值
  const initialState = {};
  props.fields.forEach(field => {
    initialState[field.prop] = { viewDisplay: true };
  });

  fieldState.value = { ...initialState };

  // 应用每个带有控制器的字段的规则
  props.fields.forEach(field => {
    // 如果字段有control函数，应用它
    if (field.control && typeof field.control === 'function') {
      const value = props.modelValue[field.prop];
      // 获取控制规则的结果
      const result = field.control(value, props.modelValue);

      // 应用控制规则结果到字段状态
      if (result) {
        Object.keys(result).forEach(key => {
          if (fieldState.value[key]) {
            fieldState.value[key] = { ...fieldState.value[key], ...result[key] };
          }
        });
      }
    }
  });
};

// 考虑联动控制后，最终可见的字段
const visibleFields = computed(() => {
  return processedFields.value.filter(field => {
    // 应用联动控制规则
    const state = fieldState.value[field.prop];
    return !state || state.viewDisplay !== false;
  });
});

// 获取字典标签
const getDictLabel = (dicData, value) => {
  if (!dicData || value === undefined || value === null) return props.emptyText;
  if ((value instanceof Array) && value.length < 1) return props.emptyText;
  const dict = dicData.find(item => item.value == value);
  return dict ? dict.label : value;
};

// 添加分隔线是否显示的逻辑判断
const shouldShowDivider = (index) => {
  // 当前分隔线后面是否有可见的表单项
  let hasVisibleFieldsAfter = false;

  // 获取当前可见字段数组
  const fields = visibleFields.value;

  // 遍历当前分隔线后面的字段，直到下一个分隔线或结束
  for (let i = index + 1; i < fields.length; i++) {
    const field = fields[i];
    // 如果遇到下一个分隔线，停止检查
    if (field.divider) {
      break;
    }
    // 如果找到了可见的非分隔线表单项
    // 注意：这里改为更明确的检查，确保字段真的可见且不是分隔线
    if (!field.divider) {
      // 检查字段是否真的可见（联动控制可能让其隐藏）
      const state = fieldState.value[field.prop];
      const isVisible = !state || state.viewDisplay !== false;

      if (isVisible) {
        hasVisibleFieldsAfter = true;
        break; // 找到一个可见字段就足够了
      }
    }
  }

  // 如果后面没有可见的表单项，不显示分隔线
  return hasVisibleFieldsAfter;
};
</script>

<style lang="scss" scoped>
.view-content {
  padding: 10px;
  width: 100%;
  overflow-x: hidden;

  .view-row {
    width: 100%;
    margin-right: 0;
    margin-left: 0;
  }

  // 添加分隔线样式
  .view-divider {
    margin: 10px 0 5px;

    .divider-wrapper {
      width: 100%;

      .divider-title {
        font-size: 16px;
        font-weight: 600;
        padding: 5px 0;
        color: #409EFF;
      }

      .divider-line {
        height: 1px;
        background-color: #EBEEF5;
        margin-bottom: 10px;
      }
    }
  }

  .view-item {
    margin-bottom: 16px;
    line-height: 24px;

    &.full-width-item {
      .view-value {
        text-align: left !important;
      }
    }

    .view-item-content {
      display: flex;
      align-items: flex-start;
    }

    .view-label {
      color: #606266;
      font-weight: 500;
      text-align: right;
      padding-right: 12px;
      flex-shrink: 0;
    }

    .view-value {
      color: #303133;
      word-break: break-all;
      text-align: left;
      flex: 1;

      .view-image {
        border-radius: 4px;
        border: 1px solid #ebeef5;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.02);
        }
      }

      .image-error {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #f5f7fa;
        color: #909399;
      }

      .multiline-text {
        white-space: pre-wrap;
        line-height: 1.5;
      }
    }
  }
}

// 查看模式下的样式优化
:deep(.el-tag) {
  margin-right: 5px;
  font-weight: normal;
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .view-divider {
    .divider-wrapper {
      .divider-title {
        color: #5e9cf3;
      }

      .divider-line {
        background-color: #363637;
      }
    }
  }
}
</style>