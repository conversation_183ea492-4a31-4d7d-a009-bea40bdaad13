package com.sux.web.controller.training;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.TrainingOrder;
import com.sux.system.service.ITrainingOrderService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训订单Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/training/order")
public class TrainingOrderController extends BaseController {
    @Autowired
    private ITrainingOrderService trainingOrderService;

    /**
     * 查询培训订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TrainingOrder trainingOrder) {
        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(trainingOrder);
        return getDataTable(list);
    }

    /**
     * 导出培训订单列表
     */
    @Log(title = "培训订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrainingOrder trainingOrder) {
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(trainingOrder);
        ExcelUtil<TrainingOrder> util = new ExcelUtil<TrainingOrder>(TrainingOrder.class);
        util.exportExcel(response, list, "培训订单数据");
    }

    /**
     * 获取培训订单详细信息
     */
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId) {
        return success(trainingOrderService.selectTrainingOrderByOrderId(orderId));
    }

    /**
     * 新增培训订单
     */
    @Log(title = "培训订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TrainingOrder trainingOrder) {
        if (!trainingOrderService.checkOrderTitleUnique(trainingOrder)) {
            return error("新增培训订单'" + trainingOrder.getOrderTitle() + "'失败，订单标题已存在");
        }
        return toAjax(trainingOrderService.insertTrainingOrder(trainingOrder));
    }

    /**
     * 修改培训订单
     */
    @Log(title = "培训订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TrainingOrder trainingOrder) {
        if (!trainingOrderService.checkOrderTitleUnique(trainingOrder)) {
            return error("修改培训订单'" + trainingOrder.getOrderTitle() + "'失败，订单标题已存在");
        }
        return toAjax(trainingOrderService.updateTrainingOrder(trainingOrder));
    }

    /**
     * 删除培训订单
     */
    @Log(title = "培训订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds) {
        return toAjax(trainingOrderService.deleteTrainingOrderByOrderIds(orderIds));
    }

    /**
     * 发布培训订单
     */
    @Log(title = "培训订单", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{orderId}")
    public AjaxResult publish(@PathVariable Long orderId) {
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(orderId);
        if (order == null) {
            return error("培训订单不存在");
        }

        // 检查订单状态，只有草稿状态才能发布
        if (!"0".equals(order.getOrderStatus())) {
            return error("只有草稿状态的订单才能发布");
        }

        // 检查必要信息是否完整
        if (order.getStartDate() == null || order.getEndDate() == null || order.getRegistrationDeadline() == null) {
            return error("请完善培训时间和报名截止时间信息");
        }

        if (order.getMaxParticipants() == null || order.getMaxParticipants() <= 0) {
            return error("请设置最大参与人数");
        }

        return toAjax(trainingOrderService.publishTrainingOrder(orderId));
    }

    /**
     * 取消培训订单
     */
    @Log(title = "培训订单", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{orderId}")
    public AjaxResult cancel(@PathVariable Long orderId) {
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(orderId);
        if (order == null) {
            return error("培训订单不存在");
        }

        // 检查订单状态，已完成的订单不能取消
        if ("3".equals(order.getOrderStatus())) {
            return error("已完成的订单不能取消");
        }

        return toAjax(trainingOrderService.cancelTrainingOrder(orderId));
    }

    /**
     * 获取即将开始的培训订单
     */
    @GetMapping("/upcoming/{days}")
    public AjaxResult getUpcomingOrders(@PathVariable int days) {
        List<TrainingOrder> list = trainingOrderService.selectUpcomingTrainingOrders(days);
        return success(list);
    }

    /**
     * 获取已过期的培训订单
     */
    @GetMapping("/expired")
    public AjaxResult getExpiredOrders() {
        List<TrainingOrder> list = trainingOrderService.selectExpiredTrainingOrders();
        return success(list);
    }

    /**
     * 获取订单状态统计
     */
    @GetMapping("/statistics")
    public AjaxResult getOrderStatistics() {
        List<TrainingOrder> list = trainingOrderService.selectOrderStatusStatistics();
        return success(list);
    }

    /**
     * 自动更新订单状态
     */
    @Log(title = "培训订单", businessType = BusinessType.UPDATE)
    @PutMapping("/auto-update-status")
    public AjaxResult autoUpdateStatus() {
        int updateCount = trainingOrderService.autoUpdateOrderStatus();
        return success("成功更新 " + updateCount + " 个订单状态");
    }
}
