import request from '@/utils/request'

// 查询政策申请列表
export function listPolicyApplication(query) {
  return request({
    url: '/policy/application/list',
    method: 'get',
    params: query
  })
}

// 查询政策申请详细
export function getPolicyApplication(applicationId) {
  return request({
    url: '/policy/application/' + applicationId,
    method: 'get'
  })
}

// 新增政策申请
export function addPolicyApplication(data) {
  return request({
    url: '/policy/application',
    method: 'post',
    data: data
  })
}

// 修改政策申请
export function updatePolicyApplication(data) {
  return request({
    url: '/policy/application',
    method: 'put',
    data: data
  })
}

// 删除政策申请
export function delPolicyApplication(applicationId) {
  return request({
    url: '/policy/application/' + applicationId,
    method: 'delete'
  })
}

// 查询待初审申请列表（初审员权限）
export function listPendingFirstReview(query) {
  return request({
    url: '/policy/application/pending-first-review',
    method: 'get',
    params: query
  })
}

// 查询待终审申请列表（终审员权限）
export function listPendingFinalReview(query) {
  return request({
    url: '/policy/application/pending-final-review',
    method: 'get',
    params: query
  })
}

// 初审操作
export function firstReview(data) {
  return request({
    url: '/policy/application/first-review',
    method: 'post',
    data: data
  })
}

// 终审操作
export function finalReview(data) {
  return request({
    url: '/policy/application/final-review',
    method: 'post',
    data: data
  })
}

// 查询审核记录
export function getApprovalRecords(applicationId) {
  return request({
    url: '/policy/application/approval-records/' + applicationId,
    method: 'get'
  })
}

// 查询我的申请列表（申请人查看自己的申请）
export function listMyApplications(query) {
  return request({
    url: '/policy/application/my-applications',
    method: 'get',
    params: query
  })
}

// 查询所有申请列表（管理员权限）
export function listAllApplications(query) {
  return request({
    url: '/policy/application/all',
    method: 'get',
    params: query
  })
}
