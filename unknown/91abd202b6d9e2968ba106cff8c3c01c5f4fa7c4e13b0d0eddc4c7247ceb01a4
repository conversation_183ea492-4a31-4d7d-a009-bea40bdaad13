<template>
  <div class="upload-file">
    <el-upload multiple :action="uploadFileUrl + fileUrl" :before-upload="handleBeforeUpload" :file-list="fileList"
      :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed" :on-success="handleUploadSuccess"
      :show-file-list="false" :headers="headers" :data="fileData" class="upload-file-uploader" ref="fileUpload">
      <!-- 上传按钮 -->
      <el-button type="primary" class="custom-btn">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType && fileType.length"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
      <span v-if="temp && temp.tempType">
        ，
        <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
          @click="importTemplate">下载模板</el-link></span>
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="file-item" v-for="(file, index) in fileList">
        <div class="file-item-content">
          <FileView :file="file" />
          <div class="file-action">
            <el-button type="danger" size="small" icon="Delete" circle @click="handleDelete(index)" />
          </div>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup name="FileUpload">
import {
  getToken
} from "@/utils/auth";
import { ref, computed, watch, getCurrentInstance } from 'vue';
import FileView from '@/components/FileView/index.vue';

const props = defineProps({
  // 值
  value: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 1,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => [],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  //上传路劲
  fileUrl: {
    type: String,
    default: "/common/upload"
  },
  fileData: {
    type: Object,
    default: () => ({
      fileBizType: 'sys_defualt_file_upload'
    })
  },
  temp: {
    type: Object,
    default: () => ({
      tempType: false
    }),
  }
});

const {
  proxy
} = getCurrentInstance();
const emit = defineEmits(['fileLoad', 'update:value']);
const number = ref(0);
const uploadList = ref([]);
const fielUrl = import.meta.env.VITE_APP_FILE_URL;
const uploadFileUrl = import.meta.env.VITE_APP_BASE_API; // 上传文件服务器地址
const headers = ref({
  'ADMIN-Authorization': "Bearer " + getToken()
});
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.value, val => {
  if (val) {
    let temp = 1;
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : val.toString().split(',');
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        item = {
          name: item,
          url: item
        };
      }
      item.uid = item.uid || new Date().getTime() + temp++;
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
}, {
  deep: true,
  immediate: true
});

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType && props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading("正在上传文件，请稍候...");
  number.value++;
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败");
  proxy.$modal.closeLoading();
  number.value--;
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({
      name: res.newFileName,
      sourceFileName: res.originalFilename,
      filePath: res.filePath,
      uid: file.uid || new Date().getTime()
    });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.fileUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  emit("fileLoad", {
    fileList: fileList.value,
    type: "del"
  });
  emit("update:value", fileList.value);
}

// 全部删除文件
function handleAllDelete(index) {
  fileList.value = [];
  emit("fileLoad", {
    fileList: fileList.value,
    type: "del"
  });
  emit("update:value", fileList.value);
};

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.fileId !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("fileLoad", {
      fileList: fileList.value,
      type: "add"
    });
    emit("update:value", fileList.value);
    proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1);
  } else {
    return name;
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].url) {
      strs += list[i].url + separator;
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : '';
}

//下载模板
function importTemplate() {
  if (props.temp && props.temp.type && props.temp.name) {
    proxy.download('monitor/open/excelTemplate/importTemplate', {
      type: props.temp.type
    }, `${props.temp.name}_${new Date().getTime()}.xlsx`)
  }
}

defineExpose({
  handleAllDelete,
});
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 16px;
}

.upload-file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 0;
}

.file-item {
  list-style: none;
  position: relative;
}

.file-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.file-action {
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 10;
}

.file-action .el-button {
  padding: 4px;
  font-size: 12px;
}
</style>
