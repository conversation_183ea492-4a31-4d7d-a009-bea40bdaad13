package com.sux.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.PolicyInfo;

import java.util.List;

/**
 * 政策信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IPolicyInfoService extends IService<PolicyInfo>
{
    /**
     * 查询政策信息列表
     * 
     * @param policyInfo 政策信息
     * @return 政策信息集合
     */
    public List<PolicyInfo> selectPolicyInfoList(PolicyInfo policyInfo);

    /**
     * 查询政策信息
     * 
     * @param policyId 政策信息主键
     * @return 政策信息
     */
    public PolicyInfo selectPolicyInfoByPolicyId(Long policyId);

    /**
     * 新增政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    public int insertPolicyInfo(PolicyInfo policyInfo);

    /**
     * 修改政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    public int updatePolicyInfo(PolicyInfo policyInfo);

    /**
     * 批量删除政策信息
     * 
     * @param policyIds 需要删除的政策信息主键集合
     * @return 结果
     */
    public int deletePolicyInfoByPolicyIds(Long[] policyIds);

    /**
     * 删除政策信息信息
     * 
     * @param policyId 政策信息主键
     * @return 结果
     */
    public int deletePolicyInfoByPolicyId(Long policyId);

    /**
     * 校验政策名称是否唯一
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    public boolean checkPolicyNameUnique(PolicyInfo policyInfo);
}
