package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.PolicyApprovalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 政策审批记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface PolicyApprovalRecordMapper extends BaseMapper<PolicyApprovalRecord>
{
    /**
     * 查询政策审批记录列表
     * 
     * @param policyApprovalRecord 政策审批记录
     * @return 政策审批记录集合
     */
    public List<PolicyApprovalRecord> selectPolicyApprovalRecordList(PolicyApprovalRecord policyApprovalRecord);

    /**
     * 查询政策审批记录
     * 
     * @param recordId 政策审批记录主键
     * @return 政策审批记录
     */
    public PolicyApprovalRecord selectPolicyApprovalRecordByRecordId(Long recordId);

    /**
     * 新增政策审批记录
     * 
     * @param policyApprovalRecord 政策审批记录
     * @return 结果
     */
    public int insertPolicyApprovalRecord(PolicyApprovalRecord policyApprovalRecord);

    /**
     * 修改政策审批记录
     * 
     * @param policyApprovalRecord 政策审批记录
     * @return 结果
     */
    public int updatePolicyApprovalRecord(PolicyApprovalRecord policyApprovalRecord);

    /**
     * 删除政策审批记录
     * 
     * @param recordId 政策审批记录主键
     * @return 结果
     */
    public int deletePolicyApprovalRecordByRecordId(Long recordId);

    /**
     * 批量删除政策审批记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyApprovalRecordByRecordIds(Long[] recordIds);

    /**
     * 根据申请ID查询审批记录
     * 
     * @param applicationId 申请ID
     * @return 审批记录集合
     */
    public List<PolicyApprovalRecord> selectRecordsByApplicationId(Long applicationId);

    /**
     * 根据申请ID和审批层级查询审批记录
     * 
     * @param applicationId 申请ID
     * @param approvalLevel 审批层级
     * @return 审批记录
     */
    public PolicyApprovalRecord selectRecordByApplicationIdAndLevel(@Param("applicationId") Long applicationId, @Param("approvalLevel") Integer approvalLevel);

    /**
     * 更新审批记录状态
     * 
     * @param recordId 记录ID
     * @param approvalStatus 审批状态
     * @param approverUserId 审批人ID
     * @param approvalComment 审批意见
     * @param approvalFiles 审批文件
     * @return 结果
     */
    public int updateApprovalRecord(@Param("recordId") Long recordId, 
                                   @Param("approvalStatus") String approvalStatus,
                                   @Param("approverUserId") Long approverUserId,
                                   @Param("approvalComment") String approvalComment,
                                   @Param("approvalFiles") String approvalFiles);
}
