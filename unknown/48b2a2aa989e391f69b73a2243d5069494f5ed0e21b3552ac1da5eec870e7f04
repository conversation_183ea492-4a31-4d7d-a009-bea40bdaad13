package com.sux.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.mapper.JobPostingMapper;
import com.sux.system.mapper.WorkerProfileMapper;
import com.sux.system.service.IJobPostingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 招聘信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class JobPostingServiceImpl extends ServiceImpl<JobPostingMapper, JobPosting> implements IJobPostingService
{
    @Autowired
    private JobPostingMapper jobPostingMapper;
    
    @Autowired
    private WorkerProfileMapper workerProfileMapper;

    /**
     * 查询招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息
     */
    @Override
    public List<JobPosting> selectJobPostingList(JobPosting jobPosting)
    {
        return jobPostingMapper.selectJobPostingList(jobPosting);
    }

    /**
     * 查询招聘信息
     * 
     * @param jobId 招聘信息主键
     * @return 招聘信息
     */
    @Override
    public JobPosting selectJobPostingByJobId(Long jobId)
    {
        return jobPostingMapper.selectJobPostingByJobId(jobId);
    }

    /**
     * 新增招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public int insertJobPosting(JobPosting jobPosting)
    {
        jobPosting.setCreateId(SecurityUtils.getUserId());
        jobPosting.setCreateTime(DateUtils.getNowDate());
        jobPosting.setPublisherUserId(SecurityUtils.getUserId());
        
        // 设置默认值
        if (StringUtils.isEmpty(jobPosting.getStatus())) {
            jobPosting.setStatus("draft");
        }
        if (StringUtils.isEmpty(jobPosting.getCurrency())) {
            jobPosting.setCurrency("CNY");
        }
        if (StringUtils.isEmpty(jobPosting.getUrgencyLevel())) {
            jobPosting.setUrgencyLevel("normal");
        }
        if (StringUtils.isEmpty(jobPosting.getPublisherType())) {
            jobPosting.setPublisherType("employer");
        }
        if (jobPosting.getPositionsAvailable() == null) {
            jobPosting.setPositionsAvailable(1);
        }
        if (jobPosting.getPositionsFilled() == null) {
            jobPosting.setPositionsFilled(0);
        }
        if (jobPosting.getViewCount() == null) {
            jobPosting.setViewCount(0);
        }
        if (jobPosting.getApplicationCount() == null) {
            jobPosting.setApplicationCount(0);
        }
        if (jobPosting.getIsVerified() == null) {
            jobPosting.setIsVerified(0);
        }
        if (jobPosting.getFeatured() == null) {
            jobPosting.setFeatured(0);
        }
        if (jobPosting.getWorkTimeFlexible() == null) {
            jobPosting.setWorkTimeFlexible(0);
        }
        
        return jobPostingMapper.insertJobPosting(jobPosting);
    }

    /**
     * 修改招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public int updateJobPosting(JobPosting jobPosting)
    {
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 批量删除招聘信息
     * 
     * @param jobIds 需要删除的招聘信息主键
     * @return 结果
     */
    @Override
    public int deleteJobPostingByJobIds(Long[] jobIds)
    {
        return jobPostingMapper.deleteJobPostingByJobIds(jobIds);
    }

    /**
     * 删除招聘信息信息
     * 
     * @param jobId 招聘信息主键
     * @return 结果
     */
    @Override
    public int deleteJobPostingByJobId(Long jobId)
    {
        return jobPostingMapper.deleteJobPostingByJobId(jobId);
    }

    /**
     * 查询我发布的招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @param publisherUserId 发布者用户ID
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectMyJobPostingList(JobPosting jobPosting, Long publisherUserId)
    {
        return jobPostingMapper.selectMyJobPostingList(jobPosting, publisherUserId);
    }

    /**
     * 查询已发布的招聘信息列表（公开接口）
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectPublishedJobPostingList(JobPosting jobPosting)
    {
        return jobPostingMapper.selectPublishedJobPostingList(jobPosting);
    }

    /**
     * 发布招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int publishJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("published");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 暂停招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int pauseJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("paused");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 关闭招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int closeJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("closed");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 完成招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int completeJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("completed");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 增加招聘信息浏览次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long jobId)
    {
        return jobPostingMapper.updateJobPostingViewCount(jobId);
    }

    /**
     * 增加招聘信息申请次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int increaseApplicationCount(Long jobId)
    {
        return jobPostingMapper.updateJobPostingApplicationCount(jobId);
    }

    /**
     * 更新招聘信息已招聘人数
     * 
     * @param jobId 招聘信息ID
     * @param positionsFilled 已招聘人数
     * @return 结果
     */
    @Override
    public int updatePositionsFilled(Long jobId, Integer positionsFilled)
    {
        return jobPostingMapper.updateJobPostingPositionsFilled(jobId, positionsFilled);
    }

    /**
     * 查询热门招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectHotJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectHotJobPostingList(limit);
    }

    /**
     * 查询推荐招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectFeaturedJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectFeaturedJobPostingList(limit);
    }

    /**
     * 查询紧急招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectUrgentJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectUrgentJobPostingList(limit);
    }

    /**
     * 根据工作类别统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountByCategory()
    {
        return jobPostingMapper.selectJobPostingCountByCategory();
    }

    /**
     * 根据工作地点统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountByLocation()
    {
        return jobPostingMapper.selectJobPostingCountByLocation();
    }

    /**
     * 根据薪资范围统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountBySalaryRange()
    {
        return jobPostingMapper.selectJobPostingCountBySalaryRange();
    }

    /**
     * 查询即将截止的招聘信息
     * 
     * @param days 天数
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectJobPostingExpiringSoon(Integer days)
    {
        return jobPostingMapper.selectJobPostingExpiringSoon(days);
    }

    /**
     * 查询招聘信息详情（包含发布者信息）
     * 
     * @param jobId 招聘信息ID
     * @return 招聘信息详情
     */
    @Override
    public JobPosting selectJobPostingDetailByJobId(Long jobId)
    {
        return jobPostingMapper.selectJobPostingDetailByJobId(jobId);
    }

    /**
     * 根据关键词搜索招聘信息
     * 
     * @param keyword 关键词
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectJobPostingByKeyword(String keyword)
    {
        return jobPostingMapper.selectJobPostingByKeyword(keyword);
    }

    /**
     * 查询相似的招聘信息
     * 
     * @param jobPosting 招聘信息
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectSimilarJobPostingList(JobPosting jobPosting, Integer limit)
    {
        return jobPostingMapper.selectSimilarJobPostingList(jobPosting, limit);
    }

    /**
     * 批量更新招聘信息状态
     * 
     * @param jobIds 招聘信息ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateJobPostingStatus(Long[] jobIds, String status)
    {
        return jobPostingMapper.batchUpdateJobPostingStatus(jobIds, status);
    }

    /**
     * 查询招聘信息统计数据
     *
     * @param publisherUserId 发布者用户ID（可选）
     * @return 统计数据
     */
    @Override
    public Map<String, Object> selectJobPostingStatistics(Long publisherUserId)
    {
        return jobPostingMapper.selectJobPostingStatistics(publisherUserId);
    }

    /**
     * 根据零工信息匹配招聘信息
     *
     * @param workerId 零工ID
     * @param limit 限制数量
     * @return 匹配的招聘信息集合
     */
    @Override
    public List<JobPosting> matchJobPostingForWorker(Long workerId, Integer limit)
    {
        // 获取零工信息
        WorkerProfile workerProfile = workerProfileMapper.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return null;
        }

        // 构建匹配参数
        Map<String, Object> matchParams = new java.util.HashMap<>();

        // 工作地点匹配
        if (StringUtils.isNotEmpty(workerProfile.getCurrentLocation())) {
            matchParams.put("workLocation", workerProfile.getCurrentLocation());
        }

        // 工作类别匹配
        if (StringUtils.isNotEmpty(workerProfile.getWorkCategories())) {
            matchParams.put("workCategories", workerProfile.getWorkCategories());
        }

        // 薪资期望匹配
        if (workerProfile.getSalaryExpectationMin() != null) {
            matchParams.put("salaryMin", workerProfile.getSalaryExpectationMin());
        }
        if (workerProfile.getSalaryExpectationMax() != null) {
            matchParams.put("salaryMax", workerProfile.getSalaryExpectationMax());
        }
        if (StringUtils.isNotEmpty(workerProfile.getSalaryTypePreference())) {
            matchParams.put("salaryType", workerProfile.getSalaryTypePreference());
        }

        // 工作时间偏好匹配
        if (StringUtils.isNotEmpty(workerProfile.getJobTypesPreferred())) {
            matchParams.put("jobTypes", workerProfile.getJobTypesPreferred());
        }

        // 技能匹配
        if (StringUtils.isNotEmpty(workerProfile.getSkills())) {
            matchParams.put("skills", workerProfile.getSkills());
        }

        // 可工作时间匹配
        if (workerProfile.getAvailabilityStartDate() != null) {
            matchParams.put("availabilityStartDate", workerProfile.getAvailabilityStartDate());
        }
        if (workerProfile.getAvailabilityEndDate() != null) {
            matchParams.put("availabilityEndDate", workerProfile.getAvailabilityEndDate());
        }

        matchParams.put("limit", limit);
        matchParams.put("status", "published");

        return jobPostingMapper.selectJobPostingByMatchParams(matchParams);
    }

    /**
     * 智能推荐招聘信息
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐的招聘信息集合
     */
    @Override
    public List<JobPosting> recommendJobPostingForUser(Long userId, Integer limit)
    {
        // 获取用户的零工信息
        WorkerProfile workerProfile = workerProfileMapper.selectWorkerProfileByUserId(userId);
        if (workerProfile != null) {
            return matchJobPostingForWorker(workerProfile.getWorkerId(), limit);
        }

        // 如果没有零工信息，返回推荐招聘信息
        return selectFeaturedJobPostingList(limit);
    }

    /**
     * 校验招聘信息标题是否唯一
     *
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public boolean checkJobTitleUnique(JobPosting jobPosting)
    {
        Long jobId = StringUtils.isNull(jobPosting.getJobId()) ? -1L : jobPosting.getJobId();
        JobPosting info = jobPostingMapper.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<JobPosting>()
                .eq(JobPosting::getJobTitle, jobPosting.getJobTitle())
                .eq(JobPosting::getPublisherUserId, jobPosting.getPublisherUserId())
                .eq(JobPosting::getDelFlag, "0")
        );
        if (StringUtils.isNotNull(info) && info.getJobId().longValue() != jobId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验招聘信息是否可以编辑
     *
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkJobPostingEditable(Long jobId, Long userId)
    {
        JobPosting jobPosting = selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return false;
        }

        // 只有发布者可以编辑
        if (!jobPosting.getPublisherUserId().equals(userId)) {
            return false;
        }

        // 已完成的招聘信息不能编辑
        if ("completed".equals(jobPosting.getStatus())) {
            return false;
        }

        return true;
    }

    /**
     * 校验招聘信息是否可以删除
     *
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkJobPostingDeletable(Long jobId, Long userId)
    {
        JobPosting jobPosting = selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return false;
        }

        // 只有发布者可以删除
        if (!jobPosting.getPublisherUserId().equals(userId)) {
            return false;
        }

        // 已有申请的招聘信息不能删除
        if (jobPosting.getApplicationCount() != null && jobPosting.getApplicationCount() > 0) {
            return false;
        }

        return true;
    }

    /**
     * 根据招聘信息匹配零工（带相似度评分）
     *
     * @param jobId 招聘信息ID
     * @param limit 限制数量
     * @return 匹配结果列表（包含零工信息和相似度评分）
     */
    @Override
    public List<Map<String, Object>> matchWorkersWithSimilarity(Long jobId, Integer limit)
    {
        // 获取招聘信息
        JobPosting jobPosting = jobPostingMapper.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return null;
        }

        // 获取匹配的零工列表
        List<WorkerProfile> workers = workerProfileMapper.selectWorkerProfileByMatchParams(buildMatchParams(jobPosting, limit));

        // 计算相似度并构建结果
        List<Map<String, Object>> results = new java.util.ArrayList<>();
        for (WorkerProfile worker : workers) {
            Double similarity = calculateJobWorkerSimilarity(jobId, worker.getWorkerId());

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("worker", worker);
            result.put("similarity", similarity);
            result.put("similarityPercentage", Math.round(similarity * 100));
            result.put("matchScore", calculateMatchScore(jobPosting, worker));

            results.add(result);
        }

        // 按相似度排序
        results.sort((a, b) -> {
            Double simA = (Double) a.get("similarity");
            Double simB = (Double) b.get("similarity");
            return simB.compareTo(simA);
        });

        return results;
    }

    /**
     * 计算招聘信息与零工的相似度
     *
     * @param jobId 招聘信息ID
     * @param workerId 零工ID
     * @return 相似度评分（0.0-1.0）
     */
    @Override
    public Double calculateJobWorkerSimilarity(Long jobId, Long workerId)
    {
        JobPosting jobPosting = jobPostingMapper.selectJobPostingByJobId(jobId);
        WorkerProfile worker = workerProfileMapper.selectWorkerProfileByWorkerId(workerId);

        if (jobPosting == null || worker == null) {
            return null;
        }

        return calculateSimilarityScore(jobPosting, worker);
    }

    /**
     * 构建匹配参数
     */
    private Map<String, Object> buildMatchParams(JobPosting jobPosting, Integer limit) {
        Map<String, Object> matchParams = new java.util.HashMap<>();

        // 工作地点匹配
        if (StringUtils.isNotEmpty(jobPosting.getWorkLocation())) {
            matchParams.put("workLocation", jobPosting.getWorkLocation());
        }

        // 工作类别匹配
        if (StringUtils.isNotEmpty(jobPosting.getJobCategory())) {
            matchParams.put("jobCategory", jobPosting.getJobCategory());
        }

        // 薪资匹配
        if (jobPosting.getSalaryMin() != null) {
            matchParams.put("salaryMin", jobPosting.getSalaryMin());
        }
        if (jobPosting.getSalaryMax() != null) {
            matchParams.put("salaryMax", jobPosting.getSalaryMax());
        }
        if (StringUtils.isNotEmpty(jobPosting.getSalaryType())) {
            matchParams.put("salaryType", jobPosting.getSalaryType());
        }

        // 工作类型匹配
        if (StringUtils.isNotEmpty(jobPosting.getJobType())) {
            matchParams.put("jobTypes", jobPosting.getJobType());
        }

        // 技能要求匹配
        if (StringUtils.isNotEmpty(jobPosting.getSkillsRequired())) {
            matchParams.put("skills", jobPosting.getSkillsRequired());
        }

        // 可工作时间匹配
        if (jobPosting.getStartDate() != null) {
            matchParams.put("availabilityStartDate", jobPosting.getStartDate());
        }
        if (jobPosting.getEndDate() != null) {
            matchParams.put("availabilityEndDate", jobPosting.getEndDate());
        }

        matchParams.put("limit", limit);
        matchParams.put("status", "active");

        return matchParams;
    }

    /**
     * 计算相似度评分
     */
    private Double calculateSimilarityScore(JobPosting jobPosting, WorkerProfile worker) {
        double totalScore = 0.0;
        int totalWeight = 0;

        // 1. 工作地点匹配 (权重: 25%)
        int locationWeight = 25;
        double locationScore = calculateLocationSimilarity(jobPosting.getWorkLocation(), worker.getCurrentLocation(), worker.getWorkLocations());
        totalScore += locationScore * locationWeight;
        totalWeight += locationWeight;

        // 2. 工作类别匹配 (权重: 30%)
        int categoryWeight = 30;
        double categoryScore = calculateCategorySimilarity(jobPosting.getJobCategory(), worker.getWorkCategories());
        totalScore += categoryScore * categoryWeight;
        totalWeight += categoryWeight;

        // 3. 薪资匹配 (权重: 20%)
        int salaryWeight = 20;
        double salaryScore = calculateSalarySimilarity(jobPosting, worker);
        totalScore += salaryScore * salaryWeight;
        totalWeight += salaryWeight;

        // 4. 技能匹配 (权重: 15%)
        int skillWeight = 15;
        double skillScore = calculateSkillSimilarity(jobPosting.getSkillsRequired(), worker.getSkills());
        totalScore += skillScore * skillWeight;
        totalWeight += skillWeight;

        // 5. 工作类型匹配 (权重: 10%)
        int typeWeight = 10;
        double typeScore = calculateJobTypeSimilarity(jobPosting.getJobType(), worker.getJobTypesPreferred());
        totalScore += typeScore * typeWeight;
        totalWeight += typeWeight;

        return totalWeight > 0 ? totalScore / totalWeight : 0.0;
    }

    /**
     * 计算地点相似度
     */
    private double calculateLocationSimilarity(String jobLocation, String workerLocation, String workerLocations) {
        if (StringUtils.isEmpty(jobLocation)) {
            return 0.5; // 如果招聘信息没有地点要求，给中等分
        }

        // 检查当前所在地
        if (StringUtils.isNotEmpty(workerLocation) && workerLocation.contains(jobLocation)) {
            return 1.0;
        }

        // 检查可工作地点
        if (StringUtils.isNotEmpty(workerLocations) && workerLocations.contains(jobLocation)) {
            return 0.9;
        }

        // 模糊匹配（同城市不同区域）
        if (StringUtils.isNotEmpty(workerLocation)) {
            String[] jobParts = jobLocation.split("市|区|县");
            String[] workerParts = workerLocation.split("市|区|县");

            if (jobParts.length > 0 && workerParts.length > 0 &&
                jobParts[0].equals(workerParts[0])) {
                return 0.7; // 同城市不同区域
            }
        }

        return 0.0;
    }

    /**
     * 计算工作类别相似度
     */
    private double calculateCategorySimilarity(String jobCategory, String workerCategories) {
        if (StringUtils.isEmpty(jobCategory)) {
            return 0.5;
        }

        if (StringUtils.isNotEmpty(workerCategories)) {
            // 完全匹配
            if (workerCategories.contains(jobCategory)) {
                return 1.0;
            }

            // 相关类别匹配（可以扩展更复杂的相关性算法）
            String[] jobCats = jobCategory.split(",|，");
            String[] workerCats = workerCategories.split(",|，");

            int matches = 0;
            for (String jobCat : jobCats) {
                for (String workerCat : workerCats) {
                    if (jobCat.trim().equals(workerCat.trim())) {
                        matches++;
                        break;
                    }
                }
            }

            if (matches > 0) {
                return (double) matches / jobCats.length;
            }
        }

        return 0.0;
    }

    /**
     * 计算薪资匹配度
     */
    private double calculateSalarySimilarity(JobPosting jobPosting, WorkerProfile worker) {
        if (jobPosting.getSalaryMin() == null && jobPosting.getSalaryMax() == null) {
            return 0.5; // 没有薪资要求
        }

        if (worker.getSalaryExpectationMin() == null && worker.getSalaryExpectationMax() == null) {
            return 0.5; // 零工没有薪资期望
        }

        double jobMin = jobPosting.getSalaryMin() != null ? jobPosting.getSalaryMin().doubleValue() : 0;
        double jobMax = jobPosting.getSalaryMax() != null ? jobPosting.getSalaryMax().doubleValue() : Double.MAX_VALUE;
        double workerMin = worker.getSalaryExpectationMin() != null ? worker.getSalaryExpectationMin().doubleValue() : 0;
        double workerMax = worker.getSalaryExpectationMax() != null ? worker.getSalaryExpectationMax().doubleValue() : Double.MAX_VALUE;

        // 计算重叠区间
        double overlapMin = Math.max(jobMin, workerMin);
        double overlapMax = Math.min(jobMax, workerMax);

        if (overlapMax >= overlapMin) {
            // 有重叠，计算重叠比例
            double overlapRange = overlapMax - overlapMin;
            double jobRange = jobMax - jobMin;
            double workerRange = workerMax - workerMin;

            if (jobRange == 0 && workerRange == 0) {
                return 1.0; // 完全匹配
            }

            double avgRange = (jobRange + workerRange) / 2;
            return avgRange > 0 ? Math.min(1.0, overlapRange / avgRange) : 1.0;
        }

        return 0.0; // 没有重叠
    }

    /**
     * 计算技能匹配度
     */
    private double calculateSkillSimilarity(String jobSkills, String workerSkills) {
        if (StringUtils.isEmpty(jobSkills)) {
            return 0.5; // 没有技能要求
        }

        if (StringUtils.isEmpty(workerSkills)) {
            return 0.0; // 零工没有技能
        }

        String[] jobSkillArray = jobSkills.split(",|，|;|；");
        String[] workerSkillArray = workerSkills.split(",|，|;|；");

        int matches = 0;
        for (String jobSkill : jobSkillArray) {
            String trimmedJobSkill = jobSkill.trim().toLowerCase();
            for (String workerSkill : workerSkillArray) {
                String trimmedWorkerSkill = workerSkill.trim().toLowerCase();
                if (trimmedJobSkill.equals(trimmedWorkerSkill) ||
                    trimmedJobSkill.contains(trimmedWorkerSkill) ||
                    trimmedWorkerSkill.contains(trimmedJobSkill)) {
                    matches++;
                    break;
                }
            }
        }

        return jobSkillArray.length > 0 ? (double) matches / jobSkillArray.length : 0.0;
    }

    /**
     * 计算工作类型匹配度
     */
    private double calculateJobTypeSimilarity(String jobType, String workerJobTypes) {
        if (StringUtils.isEmpty(jobType)) {
            return 0.5; // 没有工作类型要求
        }

        if (StringUtils.isEmpty(workerJobTypes)) {
            return 0.0; // 零工没有偏好
        }

        if (workerJobTypes.contains(jobType)) {
            return 1.0; // 完全匹配
        }

        // 相关类型匹配
        String[] jobTypes = jobType.split(",|，");
        String[] workerTypes = workerJobTypes.split(",|，");

        int matches = 0;
        for (String jType : jobTypes) {
            for (String wType : workerTypes) {
                if (jType.trim().equals(wType.trim())) {
                    matches++;
                    break;
                }
            }
        }

        return jobTypes.length > 0 ? (double) matches / jobTypes.length : 0.0;
    }

    /**
     * 计算综合匹配评分
     */
    private double calculateMatchScore(JobPosting jobPosting, WorkerProfile worker) {
        double score = 0.0;
        int factors = 0;

        // 评分因子
        if (worker.getRatingAverage() != null) {
            score += worker.getRatingAverage().doubleValue() * 20; // 评分权重20%
            factors++;
        }

        if (worker.getCompletedJobs() != null) {
            // 完成工作数量，最多加20分
            score += Math.min(20, worker.getCompletedJobs() * 2);
            factors++;
        }

        if (worker.getSuccessRate() != null) {
            score += worker.getSuccessRate().doubleValue() * 0.2; // 成功率权重20%
            factors++;
        }

        // 验证状态加分
        if (worker.getIsVerified() != null && worker.getIsVerified() == 1) {
            score += 10;
            factors++;
        }

        // 健康证加分
        if (worker.getHealthCertificate() != null) {
            score += 5;
            factors++;
        }

        return factors > 0 ? score / factors : 0.0;
    }
}
