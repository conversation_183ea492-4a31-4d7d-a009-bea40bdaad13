<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false"
    :close-on-press-escape="false" append-to-body>
    <div class="review-container">
      <!-- 申请信息展示 -->
      <div class="application-info">
        <h4>申请信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请ID">{{ applicationData.applicationId }}</el-descriptions-item>
          <el-descriptions-item label="政策名称">{{ applicationData.policyName }}</el-descriptions-item>
          <el-descriptions-item label="申请人姓名">{{ applicationData.applicantName || applicationData.applicantUserName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ applicationData.applicantPhone || '未填写' }}</el-descriptions-item>
          <el-descriptions-item label="申请人账号">{{ applicationData.applicantUserName }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ parseTime(applicationData.submitTime) }}</el-descriptions-item>
          <el-descriptions-item label="当前状态" :span="2">
            <el-tag :type="getStatusTagType(applicationData.applicationStatus)" size="small">
              {{ getStatusText(applicationData.applicationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item v-if="applicationData.remark" label="备注信息" :span="2">
            {{ applicationData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 审核表单 -->
      <div class="review-form">
        <h4>{{ reviewType === 'first' ? '初审' : '终审' }}操作</h4>
        <el-form ref="reviewFormRef" :model="reviewForm" :rules="reviewRules" label-width="100px">
          <el-form-item label="审核结果" prop="approvalStatus">
            <el-radio-group v-model="reviewForm.approvalStatus" class="review-radio-group">
              <el-radio value="1" class="review-radio-item">
                <span class="radio-content">
                  <el-icon class="radio-icon success-icon">
                    <Check />
                  </el-icon>
                  <span class="radio-text">通过</span>
                </span>
              </el-radio>
              <el-radio value="2" class="review-radio-item">
                <span class="radio-content">
                  <el-icon class="radio-icon danger-icon">
                    <Close />
                  </el-icon>
                  <span class="radio-text">拒绝</span>
                </span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审核意见" prop="approvalComment">
            <el-input v-model="reviewForm.approvalComment" type="textarea" :rows="4" placeholder="请填写审核意见"
              show-word-limit maxlength="1000" />
          </el-form-item>

        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          确认{{ reviewType === 'first' ? '初审' : '终审' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { parseTime } from "@/utils/ruoyi"
import { Check, Close } from '@element-plus/icons-vue'
const emit = defineEmits(['submit'])

const dialogVisible = ref(false)
const dialogTitle = ref('')
const reviewType = ref('') // 'first' 或 'final'
const applicationData = ref({})
const submitLoading = ref(false)
const reviewFormRef = ref(null)

// 审核表单
const reviewForm = reactive({
  approvalStatus: '',
  approvalComment: '',
  approvalFiles: []
})

// 表单验证规则
const reviewRules = {
  approvalStatus: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  approvalComment: [
    { required: true, message: '请填写审核意见', trigger: 'blur' },
    { max: 1000, message: '审核意见不能超过1000个字符', trigger: 'blur' }
  ]
}

// 打开弹窗
const openDialog = (type, title, data) => {
  reviewType.value = type
  dialogTitle.value = title
  applicationData.value = { ...data }
  dialogVisible.value = true

  // 重置表单
  resetForm()
}

// 重置表单
const resetForm = () => {
  reviewForm.approvalStatus = ''
  reviewForm.approvalComment = ''
  reviewForm.approvalFiles = []

  if (reviewFormRef.value) {
    reviewFormRef.value.resetFields()
  }
}



// 提交审核
const handleSubmit = async () => {
  if (!reviewFormRef.value) return

  try {
    await reviewFormRef.value.validate()

    submitLoading.value = true

    const payload = {
      type: reviewType.value,
      data: { ...reviewForm },
      applicationData: applicationData.value
    }

    emit('submit', payload)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
  resetForm()
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',  // 待初审
    '1': 'success',  // 初审通过
    '2': 'danger',   // 初审拒绝
    '3': 'warning',  // 待终审
    '4': 'success',  // 终审通过
    '5': 'danger',   // 终审拒绝
    '6': 'info'      // 已完成
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '待初审',
    '1': '初审通过',
    '2': '初审拒绝',
    '3': '待终审',
    '4': '终审通过',
    '5': '终审拒绝',
    '6': '已完成'
  }
  return statusMap[status] || '未知状态'
}

// 暴露方法给父组件
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style scoped>
.review-container {
  max-height: 600px;
  overflow-y: auto;
}

.application-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.application-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.review-form h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.dialog-footer {
  text-align: right;
}

.review-radio-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.review-radio-item {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  padding: 12px 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: #fafafa;
}

.review-radio-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.review-radio-item.is-checked {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.radio-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.radio-icon {
  font-size: 16px;
}

.success-icon {
  color: #67c23a;
}

.danger-icon {
  color: #f56c6c;
}

.radio-text {
  color: #303133;
  font-weight: 500;
}

:deep(.el-radio__input) {
  margin-right: 8px;
}

:deep(.el-radio__label) {
  padding-left: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-upload__tip) {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}
</style>
