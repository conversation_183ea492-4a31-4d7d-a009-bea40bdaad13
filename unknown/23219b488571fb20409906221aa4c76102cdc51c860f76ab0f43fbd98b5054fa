package com.sux.system.service;

import com.sux.system.domain.TrainingApplication;
import java.util.List;

/**
 * 培训报名Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ITrainingApplicationService 
{
    /**
     * 查询培训报名
     * 
     * @param applicationId 培训报名主键
     * @return 培训报名
     */
    public TrainingApplication selectTrainingApplicationByApplicationId(Long applicationId);

    /**
     * 查询培训报名列表
     * 
     * @param trainingApplication 培训报名
     * @return 培训报名集合
     */
    public List<TrainingApplication> selectTrainingApplicationList(TrainingApplication trainingApplication);

    /**
     * 新增培训报名
     * 
     * @param trainingApplication 培训报名
     * @return 结果
     */
    public int insertTrainingApplication(TrainingApplication trainingApplication);

    /**
     * 修改培训报名
     * 
     * @param trainingApplication 培训报名
     * @return 结果
     */
    public int updateTrainingApplication(TrainingApplication trainingApplication);

    /**
     * 批量删除培训报名
     * 
     * @param applicationIds 需要删除的培训报名主键集合
     * @return 结果
     */
    public int deleteTrainingApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除培训报名信息
     * 
     * @param applicationId 培训报名主键
     * @return 结果
     */
    public int deleteTrainingApplicationByApplicationId(Long applicationId);

    /**
     * 检查用户是否已报名某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param userId 用户ID
     * @return 报名记录
     */
    public TrainingApplication checkUserApplication(Long orderId, Long userId);

    /**
     * 检查手机号是否已报名某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param phone 手机号
     * @return 报名记录
     */
    public TrainingApplication checkPhoneApplication(Long orderId, String phone);

    /**
     * 统计某个培训订单的报名人数
     * 
     * @param orderId 培训订单ID
     * @return 报名人数
     */
    public int countApplicationsByOrderId(Long orderId);

    /**
     * 获取某个培训订单的报名列表
     * 
     * @param orderId 培训订单ID
     * @return 报名列表
     */
    public List<TrainingApplication> getApplicationsByOrderId(Long orderId);

    /**
     * 审核报名
     * 
     * @param applicationId 报名ID
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int reviewApplication(Long applicationId, String status, String reviewer, String reviewComment);

    /**
     * 提交报名申请
     * 
     * @param trainingApplication 报名信息
     * @return 结果
     */
    public int submitApplication(TrainingApplication trainingApplication);

    /**
     * 取消报名
     * 
     * @param applicationId 报名ID
     * @return 结果
     */
    public int cancelApplication(Long applicationId);

    /**
     * 检查报名唯一性
     * 
     * @param trainingApplication 报名信息
     * @return 结果
     */
    public boolean checkApplicationUnique(TrainingApplication trainingApplication);
}
