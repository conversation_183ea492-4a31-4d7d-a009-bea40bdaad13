package com.sux.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.PolicyApplication;
import com.sux.system.domain.PolicyApprovalRecord;

import java.util.List;

/**
 * 政策申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IPolicyApplicationService extends IService<PolicyApplication>
{
    /**
     * 查询政策申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPolicyApplicationList(PolicyApplication policyApplication);

    /**
     * 查询政策申请
     * 
     * @param applicationId 政策申请主键
     * @return 政策申请
     */
    public PolicyApplication selectPolicyApplicationByApplicationId(Long applicationId);

    /**
     * 新增政策申请
     * 
     * @param policyApplication 政策申请
     * @return 结果
     */
    public int insertPolicyApplication(PolicyApplication policyApplication);

    /**
     * 修改政策申请
     * 
     * @param policyApplication 政策申请
     * @return 结果
     */
    public int updatePolicyApplication(PolicyApplication policyApplication);

    /**
     * 批量删除政策申请
     * 
     * @param applicationIds 需要删除的政策申请主键集合
     * @return 结果
     */
    public int deletePolicyApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除政策申请信息
     * 
     * @param applicationId 政策申请主键
     * @return 结果
     */
    public int deletePolicyApplicationByApplicationId(Long applicationId);

    /**
     * 查询待初审申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPendingFirstReviewList(PolicyApplication policyApplication);

    /**
     * 查询待终审申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPendingFinalReviewList(PolicyApplication policyApplication);

    /**
     * 查询我的申请列表
     * 
     * @param policyApplication 政策申请
     * @param userId 用户ID
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectMyApplicationsList(PolicyApplication policyApplication, Long userId);

    /**
     * 查询所有申请列表（管理员权限）
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectAllApplicationsList(PolicyApplication policyApplication);

    /**
     * 初审操作
     * 
     * @param applicationId 申请ID
     * @param approvalStatus 审批状态（1通过 2拒绝）
     * @param approvalComment 审批意见
     * @param approvalFiles 审批文件
     * @return 结果
     */
    public int firstReview(Long applicationId, String approvalStatus, String approvalComment, String approvalFiles);

    /**
     * 终审操作
     * 
     * @param applicationId 申请ID
     * @param approvalStatus 审批状态（1通过 2拒绝）
     * @param approvalComment 审批意见
     * @param approvalFiles 审批文件
     * @return 结果
     */
    public int finalReview(Long applicationId, String approvalStatus, String approvalComment, String approvalFiles);

    /**
     * 查询审批记录
     * 
     * @param applicationId 申请ID
     * @return 审批记录集合
     */
    public List<PolicyApprovalRecord> getApprovalRecords(Long applicationId);
}
