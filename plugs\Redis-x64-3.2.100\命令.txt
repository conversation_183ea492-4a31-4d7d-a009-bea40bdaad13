redis-server.exe "redis.windows.conf"

auth Tbkj1@3#21

keys *

get x-csrf-token:dzsgkptgly

flushall



redis-cli -h 远程redis所在ip -p 6379 -a 远程redis密码


Windows下Redis设置后台启动的方法如下12：

通过CMD命令行工具进入Redis安装目录。
使用以下命令将Redis服务注册到Windows服务中：redis-server --service-install redis.windows-service.conf --loglevel verbose。

按下Win+R输入services.msc，点击确定进入服务界面，查找是否有Redis服务。

若有Redis服务，则使用命令redis-server --service-start启动Redis服务。

如果想要关闭Redis，可以使用命令redis-server --service-stop；如果想要删除Redis服务，可以使用命令redis-server --service-uninstall。