#!/bin/sh
# ./ry.sh start stag 启动 stop 停止 restart 重启 status 状态|   stag  环境
AppName=
CP_NAME=
JVM_OPTS=


#APP_HOME=`pwd`
#LOG_PATH=$APP_HOME/logs/$AppName.log
LOG_FILE=/usr/local/etc/logs/gc

# Check if log directory exists and create it if not
if [ ! -d "$LOG_FILE" ];then
        sudo mkdir -p $LOG_FILE
        echo "创建日志文件夹 $LOG_FILE"
fi

if [ "$1" = "" ];
then
    echo -e "\033[0;31m 未输入操作名 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
fi

if [ "$2" = "" ];
then
    echo -e "\033[0;31m 未输入操作环境 \033[0m  \033[0;34m {start|stop|restart|status} \033[0m"
    exit 1
fi

if [ ! -d "/usr/local/etc/backups" ];then
        sudo mkdir /usr/local/etc/backups
        echo "创建备份文件夹"
fi

if [ ! -d "/usr/local/etc/backups/$(date +%Y-%m)" ];then
        sudo mkdir /usr/local/etc/backups/$(date +%Y-%m)
        echo "创建备份文件夹"
fi

#参数赋值
# 直接使用第二个参数作为jar包名称
AppName=$2.jar
CP_NAME=$2
# JVM参数
JVM_OPTS="-XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -Xms256m -Xmx1024m -XX:MaxGCPauseMillis=100 -XX:+UseG1GC -XX:G1HeapRegionSize=4m -XX:InitiatingHeapOccupancyPercent=35 -XX:ConcGCThreads=4 -XX:ParallelGCThreads=8 -XX:+UseStringDeduplication -XX:+ExplicitGCInvokesConcurrent  -Xlog:gc*:$LOG_FILE/gc.log:time,level --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED"

if [ "$AppName" = "" ];
then
    echo -e "\033[0;31m 未输入应用名 \033[0m"
    exit 1
fi


function start()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`

	if [ x"$PID" != x"" ]; then
	    echo "$AppName is running..."
	else
		nohup java $JVM_OPTS -jar /usr/local/etc/$AppName > /dev/null 2>&1 &
		echo "Start $AppName success..."
	fi
}

function stop()
{
    echo "Stop $AppName"

	PID=""
	query(){
		PID=`ps -ef |grep java|grep $AppName|grep -v grep|awk '{print $2}'`
	}

	query
	if [ x"$PID" != x"" ]; then
		kill -TERM $PID
		echo "$AppName (pid:$PID) exiting..."
		while [ x"$PID" != x"" ]
		do
			sleep 1
			query
		done
		echo "$AppName exited."
	else
		echo "$AppName already stopped."
	fi
}

function back()
{
    sudo cp /usr/local/etc/$AppName /usr/local/etc/backups/$(date +%Y-%m)/$CP_NAME-$(date +%Y-%m-%d).jar
}

function restart()
{
	back
    stop
    sleep 2
    start
}

function status()
{
    PID=`ps -ef |grep java|grep $AppName|grep -v grep|wc -l`
    if [ $PID != 0 ];then
        echo "$AppName is running..."
    else
        echo "$AppName is not running..."
    fi
}

case $1 in
    start)
    start;;
    stop)
    stop;;
    restart)
    restart;;
    status)
    status;;
    *)

esac
