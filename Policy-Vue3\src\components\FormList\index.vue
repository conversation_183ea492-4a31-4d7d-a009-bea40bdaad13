<template>
  <div class="form-container">
    <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="labelWidth"
      :label-position="labelPosition" :disabled="isView || disabled" :inline="inline" :size="size"
      :validate-on-rule-change="false">
      <el-row :gutter="24">
        <template v-for="(item, index) in formFields" :key="index">
          <!-- 判断是否显示字段 -->
          <template v-if="isFieldVisible(item)">
            <!-- 处理分隔线 -->
            <el-col :span="24" v-if="item.divider" class="form-divider-col">
              <slot :name="item.prop" :field="item" v-if="shouldShowDivider(index)">
                <div class="form-divider-wrapper">
                  <div class="divider-title">{{ item.label }}</div>
                  <div class="divider-line"></div>
                </div>
              </slot>
            </el-col>
            <!-- 处理栅格布局 -->
            <el-col :span="item.span || 12" v-else-if="!inline">
              <el-form-item :label="item.label" :prop="item.prop" :required="item.required">
                <!-- 使用自定义插槽渲染 -->
                <slot v-if="item.formSlot" :name="item.prop" :field="item" :value="formData[item.prop]"
                  :disabled="item.disabled || isView || disabled">
                </slot>
                <!-- 根据字段类型渲染不同表单组件 -->
                <FormFieldComponent v-else :field="item" :model-value="formData[item.prop]"
                  @update:model-value="updateValue(item.prop, $event)"
                  :disabled="item.disabled || isView || disabled" />
                <div v-if="item.tip" class="el-form-item-tip">{{ item.tip }}</div>
              </el-form-item>
            </el-col>
            <!-- 内联表单模式 -->
            <el-form-item v-else :label="item.label" :prop="item.prop" :required="item.required"
              :class="{ 'form-item-block': item.block }">
              <!-- 使用自定义插槽渲染 -->
              <slot v-if="item.formSlot" :name="item.prop" :field="item" :value="formData[item.prop]"
                :disabled="item.disabled || isView || disabled">
              </slot>
              <!-- 根据字段类型渲染不同表单组件 -->
              <FormFieldComponent v-else :field="item" :model-value="formData[item.prop]"
                @update:model-value="updateValue(item.prop, $event)" :disabled="item.disabled || isView || disabled" />
              <div v-if="item.tip" class="el-form-item-tip">{{ item.tip }}</div>
            </el-form-item>
          </template>
        </template>
      </el-row>

      <!-- 自定义字段插槽 -->
      <slot name="fields"></slot>

      <!-- 表单操作按钮 -->
      <div class="form-actions" v-if="showActions">
        <slot name="actions">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading" v-if="!isView">
            {{ submitText }}
          </el-button>
        </slot>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, getCurrentInstance, onMounted, toRefs } from 'vue';
import FormFieldComponent from './components/FormFieldComponent.vue';

const props = defineProps({
  // 表单配置
  formOption: {
    type: Object,
    default: () => ({})
  },
  // 表单字段配置
  fields: {
    type: Array,
    default: () => []
  },
  // 初始表单数据
  modelValue: {
    type: Object,
    default: () => ({})
  },
  // 是否查看模式
  isView: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  // 是否禁用整个表单
  disabled: {
    type: Boolean,
    default: false
  },
  // 表单标签宽度
  labelWidth: {
    type: String,
    default: '120px'
  },
  // 表单标签位置
  labelPosition: {
    type: String,
    default: 'right'
  },
  // 是否内联表单
  inline: {
    type: Boolean,
    default: false
  },
  // 是否显示表单操作按钮
  showActions: {
    type: Boolean,
    default: true
  },
  // 提交按钮文本
  submitText: {
    type: String,
    default: '确认'
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 表单大小
  size: {
    type: String,
    default: 'default'
  },
  // 是否需要从后端获取字段配置
  fetchFields: {
    type: Boolean,
    default: false
  },
  // 菜单代码，用于获取后端配置
  menuCode: {
    type: String,
    default: ''
  },
  // 表单提交API
  submitApi: {
    type: Function,
  },
  // 表单加载API
  loadApi: {
    type: Function,
  },
  // 表单ID字段，用于加载和更新
  idField: {
    type: String,
    default: 'id'
  }
});

const emit = defineEmits(['update:modelValue', 'submit', 'cancel', 'field-change']);

const { proxy } = getCurrentInstance();

// 表单状态
const state = reactive({
  formData: { ...props.modelValue }, // 表单数据
  formFields: [...props.fields], // 表单字段配置
  formRules: {}, // 表单验证规则
  submitLoading: false, // 提交按钮加载状态
  fieldState: {}, // 存储字段状态，用于联动控制
  _applyingRules: false, // 防递归机制：使用一个标志控制递归深度
});

const { formData, formFields, formRules, submitLoading, fieldState } = toRefs(state);
const formRef = ref(null);

// 监听父组件传入的modelValue变化
watch(() => props.modelValue, (val) => {
  if (val) {
    state.formData = { ...val };
    applyControlRules(); // 应用联动规则
  }
}, { deep: true });

// 监听父组件传入的fields变化
watch(() => props.fields, (val) => {
  if (val && val.length > 0) {
    state.formFields = [...val];
    generateFormRules();
    applyControlRules(); // 应用联动规则
  }
}, { deep: true });

// 监听表单数据变化，处理联动
watch(() => state.formData, () => {
  // 使用nextTick延迟执行，避免递归更新
  proxy.$nextTick(() => {
    applyControlRules(); // 当表单数据变化时应用联动规则
    updateFieldLabels(); // 更新字段标签
    updateFieldRules(); // 更新字段验证规则
  });
}, { deep: true });

// 初始化
onMounted(() => {
  // 如果需要从后端获取字段配置
  if (props.fetchFields && props.menuCode) {
    getFieldConfig();
  } else {
    generateFormRules();
    applyControlRules(); // 初始化时应用联动规则
  }

  // 如果提供了加载API和ID，自动加载数据
  if (props.loadApi && props.modelValue && props.modelValue[props.idField]) {
    loadFormData(props.modelValue[props.idField]);
  }
});

// 获取字段配置
const getFieldConfig = async () => {
  try {
    // 假设返回的数据格式符合要求，可能需要适配处理
    const fields = [];

    // 过滤出需要在表单中显示的字段
    state.formFields = fields.filter(field =>
      field.addDisplay !== false &&
      field.editDisplay !== false
    );
    generateFormRules();
    applyControlRules(); // 获取配置后应用联动规则
  } catch (error) {
  }
};

// 加载表单数据
const loadFormData = async (id) => {
  if (!props.loadApi) return;

  try {
    const res = await props.loadApi(id);
    state.formData = { ...res.data };
    emit('update:modelValue', state.formData);
    applyControlRules(); // 加载数据后应用联动规则
  } catch (error) {
  }
};

// 生成表单验证规则
const generateFormRules = () => {
  const rules = {};

  state.formFields.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules;
    } else if (field.required) {
      // 如果没有定义规则但标记为必填，添加默认必填规则
      rules[field.prop] = [{
        required: true,
        message: `请${field.type === 'select' ? '选择' : '输入'}${field.label}`,
        trigger: ['blur', 'change']
      }];
    }
  });

  state.formRules = rules;
};

// 更新字段的验证规则
const updateFieldRules = () => {
  const rules = { ...state.formRules };

  // 遍历所有字段，检查是否有动态规则
  state.formFields.forEach((field) => {
    // 如果存在动态规则函数
    if (field.dynamicRules && typeof field.dynamicRules === 'function') {
      // 执行动态规则函数，获取新的规则
      const newRules = field.dynamicRules(state.formData);
      if (newRules) {
        rules[field.prop] = newRules;
      }
    }

    // 检查是否在状态中有规则更新
    if (state.fieldState[field.prop] && state.fieldState[field.prop].rules) {
      rules[field.prop] = state.fieldState[field.prop].rules;
    }
  });

  // 更新表单规则
  state.formRules = rules;

  // 如果表单已挂载，通知表单重新验证
  if (formRef.value) {
    // 使用nextTick确保DOM更新后再验证
    proxy.$nextTick(() => {
      formRef.value.clearValidate();
    });
  }
};

// 应用联动控制规则
const applyControlRules = () => {
  // 防递归机制：使用一个标志控制递归深度
  if (state._applyingRules) {
    return;
  }

  // 设置标志，表示正在应用规则中
  state._applyingRules = true;

  try {
    // 先初始化所有字段的状态为默认值
    const initialState = {};
    state.formFields.forEach(field => {
      initialState[field.prop] = {
        viewDisplay: true,
        addDisplay: true,
        editDisplay: true,
        disabled: field.disabled || false,
        label: field.label, // 添加初始label值
        rules: field.rules || [] // 添加初始rules值
      };
    });

    state.fieldState = { ...initialState };

    // 应用每个带有控制器的字段的规则
    state.formFields.forEach(field => {
      // 如果字段有control函数，应用它
      if (field.control && typeof field.control === 'function') {
        const value = state.formData[field.prop];
        // 获取控制规则的结果
        const result = field.control(value, state.formData);

        // 应用控制规则结果到字段状态
        if (result) {
          Object.keys(result).forEach(key => {
            if (state.fieldState[key]) {
              state.fieldState[key] = { ...state.fieldState[key], ...result[key] };
            }
          });
        }
      }
    });
  } finally {
    // 重置标志，表示规则应用完成
    state._applyingRules = false;
  }
};

// 更新字段的标签值
const updateFieldLabels = () => {
  // 遍历所有字段，检查是否有动态标签
  state.formFields.forEach((field, index) => {
    // 如果存在动态标签函数
    if (field.dynamicLabel && typeof field.dynamicLabel === 'function') {
      // 执行动态标签函数，获取新的标签值
      const newLabel = field.dynamicLabel(state.formData);
      // 更新字段的标签
      state.formFields[index].label = newLabel;

      // 同时更新字段状态中的标签
      if (state.fieldState[field.prop]) {
        state.fieldState[field.prop].label = newLabel;
      }
    }

    // 检查是否在状态中有标签更新
    if (state.fieldState[field.prop] && state.fieldState[field.prop].label) {
      state.formFields[index].label = state.fieldState[field.prop].label;
    }
  });
};

// 设置字段标签
const setFieldLabel = (prop, newLabel) => {
  // 查找字段在formFields中的索引
  const fieldIndex = state.formFields.findIndex(f => f.prop === prop);

  if (fieldIndex !== -1) {
    // 更新formFields中的label
    state.formFields[fieldIndex].label = newLabel;

    // 更新fieldState中的label
    if (state.fieldState[prop]) {
      state.fieldState[prop].label = newLabel;
    } else {
      state.fieldState[prop] = { label: newLabel };
    }
  }
};

// 判断字段是否可见
const isFieldVisible = (field) => {
  // 先检查联动控制状态
  const fieldControlState = state.fieldState[field.prop];

  // view模式下，如果设置了viewDisplay为false，则不显示
  if (props.isView) {
    if (fieldControlState && fieldControlState.viewDisplay === false) {
      return false;
    }
    if (field.viewDisplay === false) {
      return false;
    }
  }

  // 编辑模式
  if (!props.isView && props.isEdit) {
    // 如果联动控制设置为不显示，则不显示
    if (fieldControlState && fieldControlState.editDisplay === false) {
      return false;
    }
    // 如果字段配置设置为不显示，则不显示
    if (field.editDisplay === false) {
      return false;
    }
  }

  // 新增模式
  if (!props.isView && !props.isEdit) {
    // 如果联动控制设置为不显示，则不显示
    if (fieldControlState && fieldControlState.addDisplay === false) {
      return false;
    }
    // 如果字段配置设置为不显示，则不显示
    if (field.addDisplay === false) {
      return false;
    }
  }

  // 如果设置了display为函数，根据函数返回值决定是否显示
  if (typeof field.display === 'function') {
    return field.display(state.formData);
  }

  return true;
};

// 更新表单字段值并处理联动
const updateValue = (field, value) => {
  state.formData[field] = value;
  emit('update:modelValue', state.formData);
  emit('field-change', { field, value, formData: state.formData });
};

// 提交表单
const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (!valid) return;

    if (props.submitApi) {
      state.submitLoading = true;

      try {
        const res = await props.submitApi(state.formData);
        proxy.$modal.msgSuccess("保存成功");
        emit('submit', { data: state.formData, response: res });
      } catch (error) {
      } finally {
        state.submitLoading = false;
      }
    } else {
      // 如果没有提交API，直接触发submit事件
      emit('submit', { data: state.formData });
    }
  });
};

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 表单验证
const validate = () => {
  return formRef.value?.validate();
};

// 重置表单
const resetFields = () => {
  formRef.value?.resetFields();
};

// 判断分隔线是否应该显示
const shouldShowDivider = (index) => {
  // 当前分隔线后面是否有可见的表单项
  let hasVisibleFieldsAfter = false;

  // 遍历当前分隔线后面的字段，直到下一个分隔线或结束
  for (let i = index + 1; i < state.formFields.length; i++) {
    const field = state.formFields[i];
    // 如果遇到下一个可见的分隔线，停止检查
    if (field.divider && isFieldVisible(field)) {
      break;
    }
    // 如果找到了可见的非分隔线表单项
    if (isFieldVisible(field) && !field.divider) {
      hasVisibleFieldsAfter = true;
      break; // 找到一个可见字段就足够了
    }
  }

  // 如果后面没有可见的表单项，不显示分隔线
  return hasVisibleFieldsAfter;
};

// 设置字段验证规则
const setFieldRules = (prop, newRules) => {
  // 更新formRules中的rules
  state.formRules[prop] = newRules;

  // 更新fieldState中的rules
  if (state.fieldState[prop]) {
    state.fieldState[prop].rules = newRules;
  } else {
    state.fieldState[prop] = { rules: newRules };
  }

  // 清除该字段的验证状态
  if (formRef.value) {
    proxy.$nextTick(() => {
      formRef.value.clearValidate(prop);
    });
  }
};

// 对外暴露的方法
defineExpose({
  formData,
  formRef,
  validate,
  resetFields,
  submit: handleSubmit,
  applyControlRules,
  setFieldLabel, // 暴露设置字段标签的方法
  updateFieldLabels, // 暴露更新所有字段标签的方法
  setFieldRules, // 暴露设置字段验证规则的方法
  updateFieldRules // 暴露更新所有字段验证规则的方法
});
</script>

<style lang="scss" scoped>
.form-container {
  width: 100%;

  // 添加分隔线样式 - 从ViewList复制过来
  .form-divider-col {
    margin: 10px 0 5px;

    .form-divider-wrapper {
      width: 100%;

      .divider-title {
        font-size: 16px;
        font-weight: 600;
        padding: 5px 0;
        color: #409EFF;
      }

      .divider-line {
        height: 1px;
        background-color: #EBEEF5;
        margin-bottom: 10px;
      }
    }
  }

  .form-actions {
    margin-top: 20px;
    text-align: center;
    padding: 15px 0;
    border-top: 1px solid #EBEEF5;
  }

  .el-form-item-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .form-item-block {
    display: block;
    width: 100%;
  }
}
</style>
