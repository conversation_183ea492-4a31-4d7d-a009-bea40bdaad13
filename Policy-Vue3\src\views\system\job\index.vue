<template>
   <div class="job-container app-container">
      <!-- 使用 TableList 组件 -->
      <TableList v-if="isTableReady && tableColumns.length > 0" :columns="tableColumns" :data="tableData"
         :loading="tableLoading" :showIndex="true" :searchColumns="searchableColumns" :showOperation="true"
         operationLabel="操作" operationWidth="220" :fixedOperation="true" ref="tableListRef" @search="handleSearch"
         @reset="resetSearch" :defaultPage="{ pageSize: pageSize, currentPage: currentPage, total: total }"
         @current-change="handleCurrentChange" @size-change="handleSizeChange">

         <!-- 左侧按钮插槽 -->
         <template #menu-left>
            <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['monitor:job:add']">
               新 增
            </el-button>
            <el-button type="warning" class="custom-btn" @click="handleExport" v-hasPermi="['monitor:job:export']">
               导 出
            </el-button>
            <el-button type="info" class="custom-btn" @click="handleJobLog" v-hasPermi="['monitor:job:query']">
               日 志
            </el-button>
         </template>

         <!-- 任务分组列插槽 -->
         <template #jobGroup="{ row }">
            <dict-tag :options="sys_job_group" :value="row.jobGroup" />
         </template>

         <!-- 状态列插槽 -->
         <template #status="{ row }">
            <el-switch v-if="row && row.jobId && row.jobName" v-model="row.status" active-value="0" inactive-value="1"
               @change="(val) => handleStatusChange(row, val)"></el-switch>
            <span v-else>-</span>
         </template>

         <!-- cron表达式列插槽 -->
         <template #cronExpression="{ row }">
            <div class="cron-expression-cell">
               <el-tooltip v-if="row.cronExpression" :content="getCronDescription(row.cronExpression)" placement="top"
                  effect="dark" :show-after="300" :hide-after="100" popper-class="cron-tooltip">
                  <div class="cron-text" :class="{ 'invalid-cron': !isValidCron(row.cronExpression) }">
                     <el-icon class="cron-icon">
                        <Clock />
                     </el-icon>
                     <span class="cron-value">{{ row.cronExpression }}</span>
                  </div>
               </el-tooltip>
               <span v-else class="no-cron">-</span>
            </div>
         </template>

         <!-- 操作列插槽 -->
         <template #menu="{ row }">
            <div class="operation-btns">
               <el-button type="primary" link @click="handleEdit(row)" v-hasPermi="['monitor:job:edit']">
                  修改
               </el-button>
               <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['monitor:job:remove']">
                  删除
               </el-button>
               <el-button type="success" link @click="handleRun(row)" v-hasPermi="['monitor:job:changeStatus']">
                  执行
               </el-button>
               <el-button type="info" link @click="handleView(row)" v-hasPermi="['monitor:job:query']">
                  详细
               </el-button>
               <el-button type="warning" link @click="handleJobLog(row)" v-hasPermi="['monitor:job:query']">
                  日志
               </el-button>
            </div>
         </template>
      </TableList>
      <div v-else class="loading-placeholder">
         <el-empty description="正在加载表格配置..."></el-empty>
      </div>

      <!-- 表单弹窗组件 -->
      <JobFormDialog ref="jobFormDialogRef" :formFields="formFields" :formOption="formOption" @submit="handleFormSubmit"
         @cancel="handleFormCancel" />
   </div>
</template>

<script setup name="Job">
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick } from 'vue';
import { createJobTableOption } from "@/const/system/job";
import { listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus } from "@/api/monitor/job";
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils";
import { parseCronExpression, isValidCronExpression } from "@/utils/cronUtils";
import { Clock } from '@element-plus/icons-vue';
import TableList from '@/components/TableList/index.vue';
import JobFormDialog from './JobFormDialog.vue';

const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_job_group, sys_job_status } = proxy.useDict("sys_job_group", "sys_job_status");

const tableColumns = ref([]);
const searchableColumns = ref([]); // 可搜索的字段列表
const tableLoading = ref(false);
const isTableReady = ref(false);
const formOption = ref({
   dialogWidth: '900px',
   dialogHeight: '70vh'
});
const tableListRef = ref(null);
const jobFormDialogRef = ref(null);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref({});

// 表格数据
const tableData = ref([]);

// 表单字段配置
const formFields = ref([]);

// 初始化时获取数据
onMounted(() => {
   // 确保表格数据为空数组，避免undefined问题
   tableData.value = [];
   initializeConfig();
});

// 初始化配置
const initializeConfig = async () => {
   try {
      // 获取基础配置
      const baseOption = createJobTableOption(proxy);

      // 使用工具类获取合并后的配置
      const mergedConfig = await getCoSyncColumn({
         baseOption,
         proxy
      });

      // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
      const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

      // 设置表格和搜索配置
      tableColumns.value = extractedTableColumns;
      searchableColumns.value = searchColumns;

      // 设置表单字段配置
      formFields.value = extractedFormFields;

      // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
      formOption.value = {
         ...formOption.value, // 保留默认配置
         ...formOptions       // 使用从配置文件中提取的完整选项
      };

      isTableReady.value = true;

      // 加载表格数据
      loadData();
   } catch (error) {
      isTableReady.value = false;
      console.error('初始化配置失败:', error);
   }
};



// 加载表格数据
const loadData = () => {
   tableLoading.value = true;

   const queryParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchParams.value
   };

   listJob(queryParams).then(response => {
      // 确保数据完整性，过滤掉无效数据
      const validData = (response.rows || []).filter(item =>
         item && item.jobId && item.jobName
      );

      tableData.value = validData;
      total.value = response.total || 0;

      // 更新TableList组件的分页信息
      updateTablePagination();

      tableLoading.value = false;
   }).catch(error => {
      console.error('加载任务列表失败:', error);
      tableData.value = [];
      total.value = 0;
      tableLoading.value = false;
   });
};

// 更新表格分页信息
const updateTablePagination = () => {
   if (tableListRef.value && tableListRef.value.page) {
      tableListRef.value.page.total = total.value;
      tableListRef.value.page.currentPage = currentPage.value;
      tableListRef.value.page.pageSize = pageSize.value;
   }
};

// 搜索处理
const handleSearch = (params) => {
   searchParams.value = params;
   currentPage.value = 1;
   loadData();
};

// 重置搜索
const resetSearch = () => {
   searchParams.value = {};
   currentPage.value = 1;
   loadData();
};

// 分页处理
const handleCurrentChange = (page) => {
   currentPage.value = page;
   loadData();
};

const handleSizeChange = (size) => {
   pageSize.value = size;
   currentPage.value = 1;
   loadData();
};

// 任务状态修改
const handleStatusChange = (row, newStatus) => {
   // 数据验证 - 确保必要的数据存在
   if (!row || !row.jobId || !row.jobName) {
      return;
   }

   // 如果状态没有实际改变，不处理
   if (row.status === newStatus) {
      return;
   }

   let text = newStatus === "0" ? "启用" : "停用";
   proxy.$modal.confirm('确认要"' + text + '""' + row.jobName + '"任务吗?').then(function () {
      return changeJobStatus(row.jobId, newStatus);
   }).then(() => {
      proxy.$modal.msgSuccess(text + "成功");
      // 确保状态更新成功
      row.status = newStatus;
   }).catch(function () {
      // 恢复原状态
      row.status = row.status === "0" ? "1" : "0";
   });
};

// 立即执行一次
const handleRun = (row) => {
   proxy.$modal.confirm('确认要立即执行一次"' + row.jobName + '"任务吗?').then(function () {
      return runJob(row.jobId, row.jobGroup);
   }).then(() => {
      proxy.$modal.msgSuccess("执行成功");
   }).catch(() => { });
};

// 新增
const handleAdd = () => {
   jobFormDialogRef.value.openDialog('add', '新增任务');
};

// 编辑
const handleEdit = (row) => {
   getJob(row.jobId).then(response => {
      jobFormDialogRef.value.openDialog('edit', '修改任务', response.data);
   });
};

// 查看
const handleView = (row) => {
   getJob(row.jobId).then(response => {
      jobFormDialogRef.value.openDialog('view', '任务详细', response.data);
   });
};

// 删除
const handleDelete = (row) => {
   const jobIds = row.jobId;
   proxy.$modal.confirm('是否确认删除定时任务编号为"' + jobIds + '"的数据项?').then(function () {
      return delJob(jobIds);
   }).then(() => {
      loadData();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
};

// 导出
const handleExport = () => {
   proxy.download("monitor/job/export", {
      ...searchParams.value,
   }, `job_${new Date().getTime()}.xlsx`);
};

// 任务日志
const handleJobLog = (row) => {
   const jobId = row?.jobId || 0;
   router.push('/system/job-log/index/' + jobId);
};

// 处理表单提交
const handleFormSubmit = ({ type, data }) => {
   if (type === 'add') {
      addJob(data).then(response => {
         proxy.$modal.msgSuccess("新增成功");
         jobFormDialogRef.value.onSubmitSuccess();
         loadData();
      }).catch(() => {
         jobFormDialogRef.value.onSubmitError();
      });
   } else if (type === 'edit') {
      updateJob(data).then(response => {
         proxy.$modal.msgSuccess("修改成功");
         jobFormDialogRef.value.onSubmitSuccess();
         loadData();
      }).catch(() => {
         jobFormDialogRef.value.onSubmitError();
      });
   }
};

// 处理表单取消
const handleFormCancel = () => {
   // 表单取消时的处理逻辑
};

// 获取cron表达式的解释
const getCronDescription = (cronExpression) => {
   if (!cronExpression) {
      return '无cron表达式';
   }

   try {
      const description = parseCronExpression(cronExpression);
      return `${description}\n\n表达式：${cronExpression}`;
   } catch (error) {
      return `无法解析cron表达式：${cronExpression}`;
   }
};

// 检查cron表达式是否有效
const isValidCron = (cronExpression) => {
   return isValidCronExpression(cronExpression);
};
</script>

<style lang="scss" scoped>
.job-container {
   width: 100%;
   overflow-x: hidden !important; // 防止页面出现横向滚动条
   box-sizing: border-box;

   .loading-placeholder {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
   }

   .operation-btns {
      display: flex;
      gap: 1px;
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
      flex-wrap: wrap; // 允许按钮换行
      align-items: flex-start;

      .op-btn {
         padding: 2px 4px;
         font-size: 11px;
         border-radius: 3px;
         transition: all 0.3s ease;
         flex-shrink: 0; // 防止按钮被压缩
         min-width: 28px; // 设置最小宽度，适应文字
         white-space: nowrap; // 防止文字换行
         font-weight: 500;
         margin-bottom: 1px; // 换行时的垂直间距
         height: 22px;
         line-height: 1;

         &:hover {
            transform: translateY(-1px);
         }
      }
   }

   // cron表达式样式
   .cron-expression-cell {
      width: 100%;

      .cron-text {
         display: flex;
         align-items: center;
         gap: 6px;
         padding: 4px 8px;
         border-radius: 6px;
         background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
         border: 1px solid #d4e6f1;
         cursor: help;
         transition: all 0.3s ease;
         max-width: 100%;
         overflow: hidden;

         &:hover {
            background: linear-gradient(135deg, #e6f3ff 0%, #dbeafe 100%);
            border-color: #409eff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
         }

         .cron-icon {
            color: #409eff;
            font-size: 16px;
            flex-shrink: 0;
         }

         .cron-value {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            font-weight: 500;
            color: #2c3e50;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
         }

         &.invalid-cron {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-color: #f56565;

            .cron-icon {
               color: #f56565;
            }

            .cron-value {
               color: #c53030;
            }

            &:hover {
               background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
               border-color: #e53e3e;
            }
         }
      }

      .no-cron {
         color: #909399;
         font-style: italic;
      }
   }
}

// 全局tooltip样式
:global(.cron-tooltip) {
   max-width: 350px;

   .el-tooltip__content {
      font-size: 13px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-break: break-all;

      // 为表达式部分添加样式
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
   }
}
</style>
