import request from '@/utils/request'

// 查询零工信息列表
export function listWorkerProfile(query) {
  return request({
    url: '/job/worker/list',
    method: 'get',
    params: query
  })
}

// 查询活跃的零工信息列表
export function listActiveWorkerProfile(query) {
  return request({
    url: '/job/worker/active',
    method: 'get',
    params: query
  })
}

// 查询已验证的零工信息列表
export function listVerifiedWorkerProfile(query) {
  return request({
    url: '/job/worker/verified',
    method: 'get',
    params: query
  })
}

// 查询高评分零工信息
export function listHighRatedWorkerProfile(minRating = 4.0, limit = 10) {
  return request({
    url: '/job/worker/high-rated',
    method: 'get',
    params: { minRating, limit }
  })
}

// 查询经验丰富的零工信息
export function listExperiencedWorkerProfile(minExperience = 3, limit = 10) {
  return request({
    url: '/job/worker/experienced',
    method: 'get',
    params: { minExperience, limit }
  })
}

// 查询推荐零工信息
export function listRecommendedWorkerProfile(limit = 10) {
  return request({
    url: '/job/worker/recommended',
    method: 'get',
    params: { limit }
  })
}

// 查询新注册的零工信息
export function listNewWorkerProfile(days = 30, limit = 10) {
  return request({
    url: '/job/worker/new',
    method: 'get',
    params: { days, limit }
  })
}

// 根据关键词搜索零工信息
export function searchWorkerProfile(keyword) {
  return request({
    url: '/job/worker/search',
    method: 'get',
    params: { keyword }
  })
}

// ==================== 核心匹配优化接口 ====================

// 基于核心字段搜索零工信息
export function searchWorkerProfileByCore(params) {
  return request({
    url: '/job/worker/search-by-core',
    method: 'get',
    params: params
  })
}

// 根据偏好工作类型查询零工信息
export function listWorkerProfileByJobType(jobType) {
  return request({
    url: '/job/worker/by-job-type',
    method: 'get',
    params: { jobType }
  })
}

// 根据工作类别偏好查询零工信息
export function listWorkerProfileByJobCategory(jobCategory) {
  return request({
    url: '/job/worker/by-job-category',
    method: 'get',
    params: { jobCategory }
  })
}

// 根据薪资类型偏好查询零工信息
export function listWorkerProfileBySalaryType(salaryType) {
  return request({
    url: '/job/worker/by-salary-type',
    method: 'get',
    params: { salaryType }
  })
}

// 根据学历水平查询零工信息
export function listWorkerProfileByEducation(educationLevel) {
  return request({
    url: '/job/worker/by-education',
    method: 'get',
    params: { educationLevel }
  })
}

// 根据技能匹配零工信息
export function matchWorkerProfileBySkills(skills, limit = 10) {
  return request({
    url: '/job/worker/match-skills',
    method: 'get',
    params: { skills, limit }
  })
}

// 根据地理位置匹配零工信息
export function matchWorkerProfileByLocation(location, radius = 10.0, limit = 10) {
  return request({
    url: '/job/worker/match-location',
    method: 'get',
    params: { location, radius, limit }
  })
}

// 根据薪资期望匹配零工信息
export function matchWorkerProfileBySalary(salaryMin, salaryMax, salaryType) {
  return request({
    url: '/job/worker/match-salary',
    method: 'get',
    params: { salaryMin, salaryMax, salaryType }
  })
}

// 根据招聘信息匹配零工信息
export function matchWorkerProfileForJob(jobId, limit = 10) {
  return request({
    url: `/job/worker/match-job/${jobId}`,
    method: 'get',
    params: { limit }
  })
}

// 智能推荐零工信息
export function recommendWorkerProfile(limit = 10) {
  return request({
    url: '/job/worker/recommend',
    method: 'get',
    params: { limit }
  })
}

// 查询相似的零工信息
export function listSimilarWorkerProfile(workerId, limit = 5) {
  return request({
    url: `/job/worker/similar/${workerId}`,
    method: 'get',
    params: { limit }
  })
}

// 查询零工信息详细
export function getWorkerProfile(workerId) {
  return request({
    url: '/job/worker/' + workerId,
    method: 'get'
  })
}

// 获取当前用户的零工信息
export function getMyWorkerProfile() {
  return request({
    url: '/job/worker/my-profile',
    method: 'get'
  })
}

// 新增零工信息
export function addWorkerProfile(data) {
  return request({
    url: '/job/worker',
    method: 'post',
    data: data
  })
}

// 修改零工信息
export function updateWorkerProfile(data) {
  return request({
    url: '/job/worker',
    method: 'put',
    data: data
  })
}

// 激活零工信息
export function activateWorkerProfile(workerId) {
  return request({
    url: `/job/worker/activate/${workerId}`,
    method: 'put'
  })
}

// 停用零工信息
export function deactivateWorkerProfile(workerId) {
  return request({
    url: `/job/worker/deactivate/${workerId}`,
    method: 'put'
  })
}

// 暂停零工信息
export function suspendWorkerProfile(workerId) {
  return request({
    url: `/job/worker/suspend/${workerId}`,
    method: 'put'
  })
}

// 禁用零工信息
export function banWorkerProfile(workerId) {
  return request({
    url: `/job/worker/ban/${workerId}`,
    method: 'put'
  })
}

// 验证零工信息
export function verifyWorkerProfile(workerId) {
  return request({
    url: `/job/worker/verify/${workerId}`,
    method: 'put'
  })
}

// 删除零工信息
export function delWorkerProfile(workerId) {
  return request({
    url: '/job/worker/' + workerId,
    method: 'delete'
  })
}

// 批量删除零工信息
export function delWorkerProfiles(workerIds) {
  return request({
    url: '/job/worker/' + workerIds,
    method: 'delete'
  })
}

// 批量更新零工状态
export function batchUpdateWorkerProfileStatus(workerIds, status) {
  return request({
    url: '/job/worker/batch-status',
    method: 'put',
    data: { workerIds, status }
  })
}

// 更新零工评分
export function rateWorkerProfile(workerId, rating) {
  return request({
    url: `/job/worker/rate/${workerId}`,
    method: 'put',
    params: { rating }
  })
}

// 更新零工工作统计
export function updateWorkerProfileStats(workerId, isSuccess) {
  return request({
    url: `/job/worker/update-stats/${workerId}`,
    method: 'put',
    params: { isSuccess }
  })
}

// 导出零工信息
export function exportWorkerProfile(query) {
  return request({
    url: '/job/worker/export',
    method: 'post',
    params: query
  })
}

// 查询零工统计数据
export function getWorkerProfileStatistics(userId) {
  return request({
    url: '/job/worker/statistics',
    method: 'get',
    params: { userId }
  })
}

// 根据工作类别统计零工数量
export function getWorkerProfileCountByCategory() {
  return request({
    url: '/job/worker/statistics/category',
    method: 'get'
  })
}

// 根据所在地统计零工数量
export function getWorkerProfileCountByLocation() {
  return request({
    url: '/job/worker/statistics/location',
    method: 'get'
  })
}

// 根据学历统计零工数量
export function getWorkerProfileCountByEducation() {
  return request({
    url: '/job/worker/statistics/education',
    method: 'get'
  })
}

// 根据经验年数统计零工数量
export function getWorkerProfileCountByExperience() {
  return request({
    url: '/job/worker/statistics/experience',
    method: 'get'
  })
}

// 查询即将可工作的零工信息
export function listWorkerProfileAvailableSoon(days = 7) {
  return request({
    url: '/job/worker/available-soon',
    method: 'get',
    params: { days }
  })
}

// ==================== 选项列表接口 ====================

// 获取所有偏好工作类型列表
export function getJobTypesPreferredOptions() {
  return request({
    url: '/job/worker/options/job-types-preferred',
    method: 'get'
  })
}

// 获取所有工作类别偏好列表
export function getWorkCategoriesOptions() {
  return request({
    url: '/job/worker/options/work-categories',
    method: 'get'
  })
}

// 获取所有薪资类型偏好列表
export function getSalaryTypePreferencesOptions() {
  return request({
    url: '/job/worker/options/salary-type-preferences',
    method: 'get'
  })
}

// 获取所有学历水平列表
export function getEducationLevelsOptions() {
  return request({
    url: '/job/worker/options/education-levels',
    method: 'get'
  })
}
