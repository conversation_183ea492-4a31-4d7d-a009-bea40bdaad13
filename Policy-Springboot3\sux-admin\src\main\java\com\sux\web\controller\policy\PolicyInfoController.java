package com.sux.web.controller.policy;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.PolicyInfo;
import com.sux.system.service.IPolicyInfoService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 政策信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@RestController
@RequestMapping("/policy/info")
public class PolicyInfoController extends BaseController {
    @Autowired
    private IPolicyInfoService policyInfoService;

    /**
     * 查询政策信息列表
     */
//    @PreAuthorize("@ss.hasPermi('policy:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PolicyInfo policyInfo) {
        startPage();
        List<PolicyInfo> list = policyInfoService.selectPolicyInfoList(policyInfo);
        return getDataTable(list);
    }

    /**
     * 导出政策信息列表
     */
    @Log(title = "政策信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PolicyInfo policyInfo) {
        List<PolicyInfo> list = policyInfoService.selectPolicyInfoList(policyInfo);
        ExcelUtil<PolicyInfo> util = new ExcelUtil<PolicyInfo>(PolicyInfo.class);
        util.exportExcel(response, list, "政策信息数据");
    }

    /**
     * 获取政策信息详细信息
     */
    @GetMapping(value = "/{policyId}")
    public AjaxResult getInfo(@PathVariable("policyId") Long policyId) {
        return success(policyInfoService.selectPolicyInfoByPolicyId(policyId));
    }

    /**
     * 新增政策信息
     */
    @Log(title = "政策信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody PolicyInfo policyInfo) {
        if (!policyInfoService.checkPolicyNameUnique(policyInfo)) {
            return error("新增政策'" + policyInfo.getPolicyName() + "'失败，政策名称已存在");
        }
        return toAjax(policyInfoService.insertPolicyInfo(policyInfo));
    }

    /**
     * 修改政策信息
     */
    @Log(title = "政策信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody PolicyInfo policyInfo) {
        if (!policyInfoService.checkPolicyNameUnique(policyInfo)) {
            return error("修改政策'" + policyInfo.getPolicyName() + "'失败，政策名称已存在");
        }
        return toAjax(policyInfoService.updatePolicyInfo(policyInfo));
    }

    /**
     * 删除政策信息
     */
    @Log(title = "政策信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{policyIds}")
    public AjaxResult remove(@PathVariable Long[] policyIds) {
        return toAjax(policyInfoService.deletePolicyInfoByPolicyIds(policyIds));
    }
}
