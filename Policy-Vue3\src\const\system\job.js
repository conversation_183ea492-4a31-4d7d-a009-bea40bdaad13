import { parseTime } from "@/utils/ruoyi";

// 执行策略选项
export const misfirePolicyOptions = [
    {
        label: "立即执行",
        value: "1",
    },
    {
        label: "执行一次", 
        value: "2",
    },
    {
        label: "放弃执行",
        value: "3",
    },
];

// 并发执行选项
export const concurrentOptions = [
    {
        label: "允许",
        value: "0",
    },
    {
        label: "禁止",
        value: "1",
    },
];

export const createJobTableOption = (proxy) => {
    const {
        sys_job_group,
        sys_job_status
    } = proxy.useDict("sys_job_group", "sys_job_status");

    return {
        dialogWidth: '700px',  // 弹窗宽度
        dialogHeight: '70vh',  // 弹窗内容区最大高度
        labelWidth: '140px',
        column: [
            {
                label: "任务编号",
                prop: "jobId",
                minWidth: 100,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                search: false
            },
            {
                label: "任务名称",
                prop: "jobName",
                search: true,
                minWidth: 150,
                rules: [{
                    required: true,
                    message: "任务名称不能为空",
                    trigger: "blur"
                }],
                span: 12,
                showOverflowTooltip: true
            },
            {
                label: "任务分组",
                prop: "jobGroup",
                type: 'select',
                span: 12,
                search: true,
                dicData: [
                    { label: "默认", value: "DEFAULT" },
                    { label: "系统", value: "SYSTEM" }
                ],
                rules: [{
                    required: true,
                    message: "任务分组不能为空",
                    trigger: "blur"
                }],
                minWidth: 100,
                placeholder: "请选择任务分组",
                defaultValue: "DEFAULT"
            },
            {
                label: "调用目标字符串",
                prop: "invokeTarget",
                search: false,
                rules: [{
                    required: true,
                    message: "调用目标字符串不能为空",
                    trigger: "blur"
                }],
                span: 24,
                minWidth: 200,
                showOverflowTooltip: true,
                formSlot: true // 使用插槽显示帮助信息
            },
            {
                label: "cron表达式",
                prop: "cronExpression",
                search: false,
                rules: [{
                    required: true,
                    message: "cron执行表达式不能为空",
                    trigger: "change"
                }],
                span: 24,
                minWidth: 180,
                showOverflowTooltip: true,
                formSlot: true, // 使用插槽添加生成器按钮
                tableSlot: true // 使用表格插槽显示cron解释
            },
            {
                label: "执行策略",
                prop: "misfirePolicy",
                type: 'select',
                span: 12,
                dicData: misfirePolicyOptions,
                placeholder: "请选择执行策略",
                defaultValue: "1",
                addDisplay: true,
                editDisplay: true,
                viewDisplay: true,
                showColumn: false
            },
            {
                label: "是否并发",
                prop: "concurrent",
                type: 'select',
                span: 12,
                dicData: concurrentOptions,
                placeholder: "请选择并发执行",
                defaultValue: "1",
                addDisplay: true,
                editDisplay: true,
                viewDisplay: true,
                showColumn: false
            },
            {
                label: "状态",
                prop: "status",
                type: 'select',
                span: 24,
                minWidth: 100,
                search: true,
                dicData: [
                    { label: "正常", value: "0" },
                    { label: "暂停", value: "1" }
                ],
                placeholder: "请选择任务状态",
                align: "center",
                defaultValue: "0",
                slot: true,
                addDisplay: false,
            },
            {
                label: "创建时间",
                prop: "createTime",
                editDisplay: false,
                addDisplay: false,
                type: 'datetime',
                minWidth: 160,
                align: "center",
                searchRange: true,
                formatter: (val, value, label) => parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}')
            }
        ]
    };
}; 