<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <!-- 编辑/新增模式使用FormList组件 -->
            <FormList ref="formListRef" v-model="formData" :fields="currentFormFields" :is-view="dialogType === 'view'"
                :is-edit="dialogType === 'edit'" :showActions="false"
                :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange" v-if="dialogType !== 'view'">
            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="currentFormFields" :labelWidth="formOption.labelWidth">
            </ViewList>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button class="custom-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退出全屏' : '全屏显示' }}
                </el-button>
                <el-button class="custom-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关闭' : '取消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="custom-btn" @click="handleSubmitForm"
                    :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="PostFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import FormList from '@/components/FormList/index.vue';
import ViewList from '@/components/ViewList/index.vue';

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
    formFields: {
        type: Array,
        default: () => []
    },
    formOption: {
        type: Object,
        default: () => ({
            dialogWidth: '600px',
            dialogHeight: '60vh'
        })
    }
});

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 表单相关
const dialogVisible = ref(false);
const dialogType = ref('add'); // add, edit, view
const dialogTitle = ref('新增岗位');
const formListRef = ref(null);
const formData = ref({});
const submitLoading = ref(false);
const submitDisabled = ref(false);

// 控制弹窗全屏状态
const isFullscreen = ref(false);

// 根据表单类型动态筛选字段
const currentFormFields = computed(() => {
    if (!props.formFields.length) return [];

    if (dialogType.value === 'add') {
        return props.formFields.filter(field => field.addDisplay !== false);
    }
    else if (dialogType.value === 'edit') {
        return props.formFields.filter(field => field.editDisplay !== false);
    }
    else { // view模式
        return props.formFields.filter(field => field.viewDisplay !== false);
    }
});

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    const baseStyle = {
        overflow: 'visible',
        padding: '20px 10px',
        overflowX: 'hidden',
    }

    if (isFullscreen.value) {
        return {
            ...baseStyle,
            maxHeight: 'calc(100vh - 180px)',
            overflowY: 'auto',
            overflowX: 'hidden',
        }
    }

    return {
        ...baseStyle,
        maxHeight: props.formOption.dialogHeight || '60vh', // 只设置最大高度
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: 'auto', // 允许自适应最小高度
    }
});

// 切换全屏状态
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
};

// 对外暴露的方法
const openDialog = (type, title, data = {}) => {
    dialogType.value = type;
    dialogTitle.value = title;
    formData.value = { ...data };

    // 设置默认值
    if (type === 'add') {
        formData.value = {
            postSort: 0,
            status: '0',
            ...data
        };
    }

    dialogVisible.value = true;
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 处理表单字段变更
const handleFieldChange = (field, value) => {
    // 可以在这里添加字段联动逻辑
};

// 处理表单提交
const handleSubmitForm = async () => {
    // 防止重复提交
    if (submitLoading.value || submitDisabled.value) {
        return;
    }

    // 确保formListRef存在
    if (!formListRef.value) {
        return;
    }

    try {
        // 设置按钮状态
        submitLoading.value = true;
        submitDisabled.value = true;

        // 表单验证
        await formListRef.value.validate();

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        });

    } catch (error) {
        // 验证失败不显示错误消息，因为表单会自动显示错误
        submitLoading.value = false;
        submitDisabled.value = false;
    }
};

// 处理取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 处理表单对话框打开
const handleDialogOpened = () => {
    // 设置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
};

// 处理表单对话框关闭
const handleDialogClosed = () => {
    // 重置表单状态
    submitLoading.value = false;
    submitDisabled.value = false;
    isFullscreen.value = false;

    // 重置表单数据
    formData.value = {};
};

// 对外暴露的方法
defineExpose({
    openDialog,
    closeDialog
});
</script>

<style lang="scss" scoped>
.custom-btn {
    padding: 8px 20px;
}

// 弹窗内容区域样式优化
.dialog-content {
    max-height: v-bind("dialogContentStyle.maxHeight");
    min-height: v-bind("dialogContentStyle.minHeight || 'auto'");
    overflow-y: v-bind("dialogContentStyle.overflowY");
    overflow-x: hidden !important;
    padding: v-bind("dialogContentStyle.padding");
    width: 100%;
    box-sizing: border-box;

    &:not(.view-mode) {

        // 表单模式下的优化
        :deep(.el-form) {
            width: 100%;

            .el-form-item {
                margin-bottom: 18px;
                width: 100%;
                box-sizing: border-box;

                // 统一控件样式
                .el-input,
                .el-select,
                .el-date-editor,
                .el-cascader,
                .el-input-number {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;
                }

                .el-textarea {
                    width: 100%;
                    max-width: 100%;
                    box-sizing: border-box;

                    .el-textarea__inner {
                        border-radius: 6px;
                        min-height: 80px;
                        width: 100%;
                        box-sizing: border-box;
                        resize: vertical;
                    }
                }

                // 复选框和单选框样式
                .el-checkbox-group,
                .el-radio-group {
                    width: 100%;

                    .el-checkbox,
                    .el-radio {
                        margin-right: 16px;
                        margin-bottom: 8px;
                    }
                }
            }
        }
    }

    &.view-mode {
        padding: 16px 10px;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden !important;

        :deep(.view-list) {
            width: 100%;
            box-sizing: border-box;

            .el-descriptions-item__label {
                color: #606266;
                font-weight: 500;
                word-wrap: break-word;
                word-break: break-all;
            }

            .el-descriptions-item__content {
                color: #303133;
                word-wrap: break-word;
                word-break: break-all;
                max-width: 100%;
                overflow-wrap: break-word;
            }
        }
    }

    // 分隔线样式
    .form-divider-wrapper {
        width: 100%;
        margin: 20px 0 15px 0;

        .divider-title {
            font-size: 16px;
            font-weight: 600;
            padding: 8px 0;
            color: var(--el-color-primary);
            display: flex;
            align-items: center;

            &::before {
                content: '';
                width: 4px;
                height: 16px;
                background-color: var(--el-color-primary);
                margin-right: 8px;
                border-radius: 2px;
            }
        }

        .divider-line {
            height: 1px;
            background: linear-gradient(to right, var(--el-color-primary-light-5), transparent);
            margin-bottom: 15px;
        }
    }
}

// 弹窗底部样式优化
:deep(.el-dialog__footer) {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f2f5;
    background: linear-gradient(180deg, #fafbfc 0%, #f8f9fa 100%);
    border-radius: 0 0 12px 12px;

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;
    }
}

// 弹窗主体样式优化
:deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .el-dialog__header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f2f5;
        background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
        border-radius: 12px 12px 0 0;
        overflow: hidden;

        .el-dialog__title {
            color: #303133;
            font-weight: 600;
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .el-dialog__body {
        padding: 0;
        background: #ffffff;
        overflow-x: hidden !important;
        width: 100%;
        box-sizing: border-box;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .dialog-content {
        .form-divider-wrapper {
            margin: 15px 0 10px 0;

            .divider-title {
                font-size: 14px;
                padding: 6px 0;
            }
        }
    }

    :deep(.el-dialog__footer) {
        .dialog-footer {
            flex-direction: column;
            gap: 8px;

            .custom-btn {
                width: 100%;
                margin: 0;
            }
        }
    }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
    .dialog-content {
        .form-divider-wrapper {
            .divider-title {
                color: var(--el-color-primary-light-3);

                &::before {
                    background-color: var(--el-color-primary-light-3);
                }
            }

            .divider-line {
                background: linear-gradient(to right, var(--el-color-primary-dark-2), transparent);
            }
        }
    }
}
</style>