package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.PolicyApplication;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 政策申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface PolicyApplicationMapper extends BaseMapper<PolicyApplication>
{
    /**
     * 查询政策申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPolicyApplicationList(PolicyApplication policyApplication);

    /**
     * 查询政策申请
     * 
     * @param applicationId 政策申请主键
     * @return 政策申请
     */
    public PolicyApplication selectPolicyApplicationByApplicationId(Long applicationId);

    /**
     * 新增政策申请
     * 
     * @param policyApplication 政策申请
     * @return 结果
     */
    public int insertPolicyApplication(PolicyApplication policyApplication);

    /**
     * 修改政策申请
     * 
     * @param policyApplication 政策申请
     * @return 结果
     */
    public int updatePolicyApplication(PolicyApplication policyApplication);

    /**
     * 删除政策申请
     * 
     * @param applicationId 政策申请主键
     * @return 结果
     */
    public int deletePolicyApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除政策申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 查询待初审申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPendingFirstReviewList(PolicyApplication policyApplication);

    /**
     * 查询待终审申请列表
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectPendingFinalReviewList(PolicyApplication policyApplication);

    /**
     * 查询我的申请列表
     * 
     * @param policyApplication 政策申请
     * @param userId 用户ID
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectMyApplicationsList(@Param("policyApplication") PolicyApplication policyApplication, @Param("userId") Long userId);

    /**
     * 查询所有申请列表（管理员权限）
     * 
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    public List<PolicyApplication> selectAllApplicationsList(PolicyApplication policyApplication);

    /**
     * 更新申请状态
     * 
     * @param applicationId 申请ID
     * @param status 新状态
     * @return 结果
     */
    public int updateApplicationStatus(@Param("applicationId") Long applicationId, @Param("status") String status);

    /**
     * 完成申请（设置完成时间）
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int completeApplication(Long applicationId);
}
