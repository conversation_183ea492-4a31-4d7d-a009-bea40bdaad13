/*
 Navicat Premium Data Transfer

 Source Server         : 8.0.40_3306
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : bussine-web-policy

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 23/07/2025 00:41:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for job_application
-- ----------------------------
DROP TABLE IF EXISTS `job_application`;
CREATE TABLE `job_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `job_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `application_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending' COMMENT '申请状态（pending/accepted/rejected/withdrawn/completed）',
  `application_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '申请留言',
  `employer_response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '雇主回复',
  `interview_time` datetime(0) NULL DEFAULT NULL COMMENT '面试时间',
  `interview_location` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '面试地点',
  `interview_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '面试备注',
  `start_work_time` datetime(0) NULL DEFAULT NULL COMMENT '开始工作时间',
  `end_work_time` datetime(0) NULL DEFAULT NULL COMMENT '结束工作时间',
  `actual_salary` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际薪资',
  `work_rating` decimal(3, 2) NULL DEFAULT NULL COMMENT '工作评分',
  `work_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作反馈',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_job_worker`(`job_id`, `worker_id`) USING BTREE,
  INDEX `idx_job_id`(`job_id`) USING BTREE,
  INDEX `idx_status`(`application_status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_application
-- ----------------------------

-- ----------------------------
-- Table structure for job_match_record
-- ----------------------------
DROP TABLE IF EXISTS `job_match_record`;
CREATE TABLE `job_match_record`  (
  `match_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `job_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5, 2) NOT NULL COMMENT '匹配分数（0-100）',
  `match_factors` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '匹配因素详情（JSON格式）',
  `match_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'system' COMMENT '匹配类型（system/manual）',
  `is_viewed_by_employer` tinyint(1) NULL DEFAULT 0 COMMENT '雇主是否已查看（0否 1是）',
  `is_viewed_by_worker` tinyint(1) NULL DEFAULT 0 COMMENT '零工是否已查看（0否 1是）',
  `employer_interest` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '雇主兴趣（interested/not_interested/contacted）',
  `worker_interest` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '零工兴趣（interested/not_interested/applied）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`match_id`) USING BTREE,
  UNIQUE INDEX `uk_job_worker_match`(`job_id`, `worker_id`) USING BTREE,
  INDEX `idx_job_id`(`job_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_match_score`(`match_score`) USING BTREE,
  INDEX `idx_match_type`(`match_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工作匹配记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_match_record
-- ----------------------------

-- ----------------------------
-- Table structure for job_posting
-- ----------------------------
DROP TABLE IF EXISTS `job_posting`;
CREATE TABLE `job_posting`  (
  `job_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '职位名称',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位描述',
  `job_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作类型（全职/兼职/临时工/小时工）',
  `job_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售等）',
  `work_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工作地点',
  `work_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细工作地址',
  `salary_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高薪资',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'CNY' COMMENT '货币单位',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日工作小时数',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周工作天数',
  `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '结束日期',
  `work_time_flexible` tinyint(1) NULL DEFAULT 0 COMMENT '工作时间是否灵活（0否 1是）',
  `experience_required` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验要求',
  `education_required` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历要求',
  `age_min` int(0) NULL DEFAULT NULL COMMENT '最小年龄要求',
  `age_max` int(0) NULL DEFAULT NULL COMMENT '最大年龄要求',
  `gender_requirement` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别要求（male/female/any）',
  `skills_required` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能要求（JSON格式）',
  `language_required` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '语言要求',
  `physical_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '体力要求描述',
  `benefits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '福利待遇',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `contact_wechat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信号',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司地址',
  `company_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '公司描述',
  `urgency_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `positions_available` int(0) NULL DEFAULT 1 COMMENT '招聘人数',
  `positions_filled` int(0) NULL DEFAULT 0 COMMENT '已招聘人数',
  `application_deadline` datetime(0) NULL DEFAULT NULL COMMENT '申请截止时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(0) NULL DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `publisher_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'employer' COMMENT '发布者类型（employer/agency/individual）',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `verification_time` datetime(0) NULL DEFAULT NULL COMMENT '验证时间',
  `featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`job_id`) USING BTREE,
  INDEX `idx_job_type`(`job_type`) USING BTREE,
  INDEX `idx_job_category`(`job_category`) USING BTREE,
  INDEX `idx_work_location`(`work_location`) USING BTREE,
  INDEX `idx_salary_range`(`salary_min`, `salary_max`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_publisher`(`publisher_user_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_urgency`(`urgency_level`) USING BTREE,
  INDEX `idx_featured`(`featured`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of job_posting
-- ----------------------------
INSERT INTO `job_posting` VALUES (1, '餐厅服务员', '负责餐厅日常服务工作，包括点餐、上菜、收银等', '兼职', '服务员', '北京市朝阳区', '北京市朝阳区三里屯太古里', 'hourly', 25.00, 35.00, 'CNY', 8, 5, '2025-08-01', NULL, 0, '无经验要求', NULL, 18, 45, 'any', NULL, NULL, NULL, NULL, '张经理', '13800138001', NULL, NULL, '美味餐厅', NULL, NULL, 'normal', 3, 0, NULL, 'published', 0, 0, 1, 'employer', 0, NULL, 0, 1, '2025-07-23 00:22:06', NULL, '2025-07-23 00:22:06', '0', NULL);
INSERT INTO `job_posting` VALUES (2, '保洁员', '负责办公楼保洁工作，包括地面清洁、垃圾清理等', '全职', '保洁', '上海市浦东新区', '上海市浦东新区陆家嘴金融中心', 'monthly', 4000.00, 5000.00, 'CNY', 8, 6, '2025-08-05', NULL, 0, '有相关经验优先', NULL, 25, 55, 'any', NULL, NULL, NULL, NULL, '李主管', '13900139001', NULL, NULL, '清洁服务公司', NULL, NULL, 'normal', 2, 0, NULL, 'published', 0, 0, 2, 'employer', 0, NULL, 0, 2, '2025-07-23 00:22:06', NULL, '2025-07-23 00:22:06', '0', NULL);

-- ----------------------------
-- Table structure for match_record
-- ----------------------------
DROP TABLE IF EXISTS `match_record`;
CREATE TABLE `match_record`  (
  `match_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `recruitment_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5, 2) NULL COMMENT '匹配分数(0-100)',
  `skill_match_score` decimal(5, 2) NULL COMMENT '技能匹配分数',
  `location_match_score` decimal(5, 2) NULL COMMENT '地点匹配分数',
  `salary_match_score` decimal(5, 2) NULL COMMENT '薪资匹配分数',
  `experience_match_score` decimal(5, 2) NULL COMMENT '经验匹配分数',
  `education_match_score` decimal(5, 2) NULL COMMENT '学历匹配分数',
  `time_match_score` decimal(5, 2) NULL COMMENT '时间匹配分数',
  `match_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '匹配详情(JSON格式)',
  `match_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '匹配状态(0待处理 1已推荐 2已申请 3已拒绝)',
  `is_mutual_match` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否双向匹配(0否 1是)',
  `employer_viewed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '雇主是否已查看(0否 1是)',
  `worker_viewed` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '零工是否已查看(0否 1是)',
  `match_time` datetime(0) NULL DEFAULT NULL COMMENT '匹配时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`match_id`) USING BTREE,
  UNIQUE INDEX `uk_recruitment_worker`(`recruitment_id`, `worker_id`) USING BTREE,
  INDEX `idx_recruitment_id`(`recruitment_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_match_score`(`match_score`) USING BTREE,
  INDEX `idx_match_status`(`match_status`) USING BTREE,
  INDEX `idx_match_time`(`match_time`) USING BTREE,
  CONSTRAINT `fk_match_record_recruitment` FOREIGN KEY (`recruitment_id`) REFERENCES `recruitment_info` (`recruitment_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_match_record_worker` FOREIGN KEY (`worker_id`) REFERENCES `worker_info` (`worker_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '匹配记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of match_record
-- ----------------------------

-- ----------------------------
-- Table structure for policy_application
-- ----------------------------
DROP TABLE IF EXISTS `policy_application`;
CREATE TABLE `policy_application`  (
  `application_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `policy_id` bigint(0) NOT NULL COMMENT '政策ID',
  `applicant_user_id` bigint(0) NOT NULL COMMENT '申请人用户ID',
  `applicant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人姓名',
  `applicant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '申请人手机号',
  `application_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '申请状态（0待初审 1初审通过 2初审拒绝 3待终审 4终审通过 5终审拒绝 6已完成）',
  `required_materials` json NULL COMMENT '所需材料JSON数据',
  `submit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
  `complete_time` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`application_id`) USING BTREE,
  INDEX `idx_policy_id`(`policy_id`) USING BTREE,
  INDEX `idx_applicant_user_id`(`applicant_user_id`) USING BTREE,
  INDEX `idx_application_status`(`application_status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_applicant_name`(`applicant_name`) USING BTREE,
  INDEX `idx_applicant_phone`(`applicant_phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策申请表（包含申请人基本信息）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_application
-- ----------------------------
INSERT INTO `policy_application` VALUES (1, 1, 2, NULL, NULL, '5', '[{\"name\": \"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\", \"type\": \"form\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业的营业执照副本复印件\", \"type\": \"license\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"退役军人需提供退伍证原件及复印件\", \"type\": \"certificate\", \"required\": false, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业社保缴费凭证\", \"type\": \"proof\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}, {\"name\": \"企业在银行开立的基本账户证明\", \"type\": \"account\", \"required\": true, \"uploaded\": false, \"file_path\": \"\"}]', '2025-07-21 00:00:00', NULL, '0', 2, '2025-07-21 00:00:00', NULL, NULL, '示例政策申请');
INSERT INTO `policy_application` VALUES (2, 1, 102, '张三', '***********', '0', '[{\"name\": \"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\", \"files\": [{\"uid\": *************, \"name\": \"【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\", \"sourceFileName\": \"【哲风壁纸】二次元场景-动漫插画.png\"}], \"required\": true}, {\"name\": \"企业的营业执照副本复印件\", \"files\": [{\"uid\": *************, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}, {\"name\": \"退役军人需提供退伍证原件及复印件\", \"files\": [{\"uid\": 1753200299883, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": false}, {\"name\": \"企业社保缴费凭证\", \"files\": [{\"uid\": 1753200302121, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}, {\"name\": \"企业在银行开立的基本账户\", \"files\": [{\"uid\": 1753200303955, \"name\": \"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\", \"status\": \"success\", \"filePath\": \"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\", \"sourceFileName\": \"【哲风壁纸】可爱-可爱猫-大眼猫.png\"}], \"required\": true}]', '2025-07-23 00:05:15', NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, '');

-- ----------------------------
-- Table structure for policy_approval_record
-- ----------------------------
DROP TABLE IF EXISTS `policy_approval_record`;
CREATE TABLE `policy_approval_record`  (
  `record_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '审批记录ID',
  `application_id` bigint(0) NOT NULL COMMENT '申请ID',
  `approval_level` tinyint(0) NOT NULL COMMENT '审批层级（1初审 2终审）',
  `approval_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '审批状态（0待审批 1审批通过 2审批拒绝）',
  `approver_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审批人用户ID',
  `approval_time` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `approval_comment` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审批意见',
  `approval_files` json NULL COMMENT '审批相关文件JSON数据',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`) USING BTREE,
  INDEX `idx_application_id`(`application_id`) USING BTREE,
  INDEX `idx_approval_level`(`approval_level`) USING BTREE,
  INDEX `idx_approval_status`(`approval_status`) USING BTREE,
  INDEX `idx_approver_user_id`(`approver_user_id`) USING BTREE,
  INDEX `idx_approval_time`(`approval_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策审批记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_approval_record
-- ----------------------------
INSERT INTO `policy_approval_record` VALUES (1, 1, 1, '1', 100, '2025-07-23 00:08:53', 'ces ', NULL, '0', 1, '2025-07-21 00:00:00', NULL, NULL, '初审记录');
INSERT INTO `policy_approval_record` VALUES (2, 1, 2, '2', 101, '2025-07-23 00:09:25', 'ceshi ', NULL, '0', 1, '2025-07-21 00:00:00', NULL, NULL, '终审记录');
INSERT INTO `policy_approval_record` VALUES (3, 2, 1, '0', NULL, NULL, NULL, NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, NULL);
INSERT INTO `policy_approval_record` VALUES (4, 2, 2, '0', NULL, NULL, NULL, NULL, '0', 102, '2025-07-23 00:05:15', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for policy_info
-- ----------------------------
DROP TABLE IF EXISTS `policy_info`;
CREATE TABLE `policy_info`  (
  `policy_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '政策ID',
  `policy_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '政策名称',
  `policy_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '政策描述',
  `policy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '政策类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`policy_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '政策信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policy_info
-- ----------------------------
INSERT INTO `policy_info` VALUES (1, '企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴', '为鼓励企业吸纳就业困难人员、高校毕业生和退役军人就业，给予企业相应的社会保险补贴。申请企业需要提供相关证明材料，经过初审和终审两级审批后，方可获得补贴。', '就业扶持', '0', '0', 1, '2025-07-21 00:00:00', NULL, NULL, '就业扶持政策');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint(0) NOT NULL COMMENT '触发的时间',
  `sched_time` bigint(0) NOT NULL COMMENT '定时器制定的时间',
  `priority` int(0) NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '已触发的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '暂停的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint(0) NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint(0) NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调度器状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint(0) NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint(0) NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint(0) NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int(0) NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int(0) NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint(0) NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint(0) NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint(0) NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint(0) NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int(0) NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint(0) NOT NULL COMMENT '开始时间',
  `end_time` bigint(0) NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint(0) NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name`, `job_name`, `job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发器详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for recruitment_info
-- ----------------------------
DROP TABLE IF EXISTS `recruitment_info`;
CREATE TABLE `recruitment_info`  (
  `recruitment_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '职位名称',
  `company_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司名称',
  `job_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位描述',
  `job_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位要求',
  `work_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作地点',
  `work_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作类型(全职/兼职/临时)',
  `salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低薪资',
  `salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高薪资',
  `salary_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'month' COMMENT '薪资单位(hour/day/month)',
  `education_requirement` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历要求',
  `experience_requirement` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经验要求',
  `age_min` int(0) NULL DEFAULT NULL COMMENT '最小年龄要求',
  `age_max` int(0) NULL DEFAULT NULL COMMENT '最大年龄要求',
  `gender_requirement` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别要求(0男 1女 2不限)',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `recruitment_count` int(0) NULL DEFAULT 1 COMMENT '招聘人数',
  `current_applicants` int(0) NULL DEFAULT 0 COMMENT '当前申请人数',
  `work_start_date` date NULL DEFAULT NULL COMMENT '工作开始日期',
  `work_end_date` date NULL DEFAULT NULL COMMENT '工作结束日期',
  `application_deadline` date NULL DEFAULT NULL COMMENT '申请截止日期',
  `is_urgent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否紧急(0否 1是)',
  `is_featured` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否推荐(0否 1是)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0发布中 1已暂停 2已结束）',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `company_logo` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '公司logo',
  `job_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '职位相关图片(JSON格式)',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`recruitment_id`) USING BTREE,
  INDEX `idx_publisher_user_id`(`publisher_user_id`) USING BTREE,
  INDEX `idx_work_location`(`work_location`) USING BTREE,
  INDEX `idx_work_type`(`work_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_application_deadline`(`application_deadline`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of recruitment_info
-- ----------------------------

-- ----------------------------
-- Table structure for recruitment_skill
-- ----------------------------
DROP TABLE IF EXISTS `recruitment_skill`;
CREATE TABLE `recruitment_skill`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `recruitment_id` bigint(0) NOT NULL COMMENT '招聘ID',
  `skill_id` bigint(0) NOT NULL COMMENT '技能ID',
  `skill_level_required` tinyint(0) NULL DEFAULT 1 COMMENT '要求技能等级(1-初级 2-中级 3-高级)',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否必需(0否 1是)',
  `weight` decimal(3, 2) NULL DEFAULT 1.00 COMMENT '权重(0-1)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_recruitment_skill`(`recruitment_id`, `skill_id`) USING BTREE,
  INDEX `idx_recruitment_id`(`recruitment_id`) USING BTREE,
  INDEX `idx_skill_id`(`skill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '招聘技能关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of recruitment_skill
-- ----------------------------

-- ----------------------------
-- Table structure for skill_tag
-- ----------------------------
DROP TABLE IF EXISTS `skill_tag`;
CREATE TABLE `skill_tag`  (
  `skill_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '技能ID',
  `skill_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '技能名称',
  `skill_category` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能分类',
  `skill_level` tinyint(0) NULL DEFAULT 1 COMMENT '技能等级要求(1-初级 2-中级 3-高级)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`skill_id`) USING BTREE,
  UNIQUE INDEX `uk_skill_name`(`skill_name`) USING BTREE,
  INDEX `idx_skill_category`(`skill_category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '技能标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of skill_tag
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-light', 'Y', 1, '2025-06-07 19:23:03', 1, '2025-06-28 11:08:45', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (7, '用户管理-初始密码修改策略', 'sys.account.initPasswordModify', '1', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '0：初始密码修改策略关闭，没有任何提示，1：提醒用户，如果未修改初始密码，则在登录时就会提醒修改密码对话框');
INSERT INTO `sys_config` VALUES (8, '用户管理-账号密码更新周期', 'sys.account.passwordValidateDays', '0', 'Y', 1, '2025-06-07 19:23:03', NULL, NULL, '密码更新周期（填写数字，数据初始化值为0不限制，若修改必须为大于0小于365的正整数），如果超过这个周期登录系统时，则在登录时就会提醒修改密码对话框');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(0) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(0) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 200 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 1, '2025-06-07 19:23:02', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(0) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 1, '2025-06-07 19:23:03', 1, '2025-06-28 13:29:07', '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------
INSERT INTO `sys_job_log` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：6803毫秒', '0', '', '2025-06-28 10:58:28');
INSERT INTO `sys_job_log` VALUES (2, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：10432毫秒', '0', '', '2025-06-28 10:59:35');
INSERT INTO `sys_job_log` VALUES (3, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：58246毫秒', '0', '', '2025-06-28 11:05:19');
INSERT INTO `sys_job_log` VALUES (4, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：12896毫秒', '1', 'java.lang.reflect.InvocationTargetException\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:61)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)\r\n	at com.sux.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)\r\n	at com.sux.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:42)\r\n	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)\r\n	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)\r\nCaused by: com.sux.common.exception.ServiceException: 测试多数据\r\n	at com.sux.system.service.impl.SysConfigServiceImpl.selectConfigByS(SysConfigServiceImpl.java:228)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)\r\n	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)\r\n	at com.sux.framework.aspectj.DataSourceAspect.around(DataSourceAspect.java:49)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)\r\n	at org.springframework.aop.aspectj', '2025-06-28 11:07:24');
INSERT INTO `sys_job_log` VALUES (5, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '系统默认（无参） 总共耗时：9823毫秒', '1', 'java.lang.reflect.InvocationTargetException\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:61)\r\n	at com.sux.quartz.util.JobInvokeUtil.invokeMethod(JobInvokeUtil.java:33)\r\n	at com.sux.quartz.util.QuartzDisallowConcurrentExecution.doExecute(QuartzDisallowConcurrentExecution.java:19)\r\n	at com.sux.quartz.util.AbstractQuartzJob.execute(AbstractQuartzJob.java:42)\r\n	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)\r\n	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)\r\nCaused by: com.sux.common.exception.ServiceException: 测试多数据\r\n	at com.sux.system.service.impl.SysConfigServiceImpl.selectConfigByS(SysConfigServiceImpl.java:228)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)\r\n	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)\r\n	at com.sux.framework.aspectj.DataSourceAspect.around(DataSourceAspect.java:49)\r\n	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n	at java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)\r\n	at org.springframework.aop.aspectj', '2025-06-28 11:08:55');

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime(0) NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 225 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-26 20:37:40');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 20:22:05');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-27 23:33:11');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 10:20:34');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-28 21:57:29');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 03:56:16');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-06-29 19:42:13');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-06-29 19:42:18');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-29 19:42:22');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-06-30 19:45:27');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-09 21:58:11');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-09 22:00:26');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-07-09 22:04:20');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-09 22:04:28');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-10 21:46:44');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-10 21:46:59');
INSERT INTO `sys_logininfor` VALUES (116, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:20');
INSERT INTO `sys_logininfor` VALUES (117, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:27');
INSERT INTO `sys_logininfor` VALUES (118, '从沙发上', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-10 22:24:48');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-10 22:42:16');
INSERT INTO `sys_logininfor` VALUES (120, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 21:03:08');
INSERT INTO `sys_logininfor` VALUES (121, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 21:07:31');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 21:07:43');
INSERT INTO `sys_logininfor` VALUES (123, '12321453', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '用户不存在/密码错误', '2025-07-11 21:18:32');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:18:41');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-11 21:21:42');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 21:21:47');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:21:52');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:23');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:34');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 21:22:52');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 21:22:55');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:14:56');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:24:54');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:25:52');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:22');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:23');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:25');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:26:26');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '1', '验证码已失效', '2025-07-11 22:27:02');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:27:41');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:33:18');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:33:24');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:33:27');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:33:34');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:33:40');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:33:47');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:10');
INSERT INTO `sys_logininfor` VALUES (150, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:34:12');
INSERT INTO `sys_logininfor` VALUES (151, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:22');
INSERT INTO `sys_logininfor` VALUES (152, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:34:28');
INSERT INTO `sys_logininfor` VALUES (153, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:34:49');
INSERT INTO `sys_logininfor` VALUES (154, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:35:17');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:35:25');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:36:11');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:37:15');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:37:18');
INSERT INTO `sys_logininfor` VALUES (159, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:41:57');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:42:06');
INSERT INTO `sys_logininfor` VALUES (161, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:42:11');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:43:21');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:43:33');
INSERT INTO `sys_logininfor` VALUES (164, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:43:43');
INSERT INTO `sys_logininfor` VALUES (165, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:44:02');
INSERT INTO `sys_logininfor` VALUES (166, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:44:05');
INSERT INTO `sys_logininfor` VALUES (167, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 22:44:30');
INSERT INTO `sys_logininfor` VALUES (168, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 22:44:43');
INSERT INTO `sys_logininfor` VALUES (169, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 22:44:59');
INSERT INTO `sys_logininfor` VALUES (170, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 22:55:04');
INSERT INTO `sys_logininfor` VALUES (171, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:01:08');
INSERT INTO `sys_logininfor` VALUES (172, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-11 23:01:12');
INSERT INTO `sys_logininfor` VALUES (173, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-11 23:01:20');
INSERT INTO `sys_logininfor` VALUES (174, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:01:32');
INSERT INTO `sys_logininfor` VALUES (175, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:06:22');
INSERT INTO `sys_logininfor` VALUES (176, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:06:25');
INSERT INTO `sys_logininfor` VALUES (177, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:07:29');
INSERT INTO `sys_logininfor` VALUES (178, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:07:31');
INSERT INTO `sys_logininfor` VALUES (179, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:07:37');
INSERT INTO `sys_logininfor` VALUES (180, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:07:41');
INSERT INTO `sys_logininfor` VALUES (181, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:08:38');
INSERT INTO `sys_logininfor` VALUES (182, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:08:40');
INSERT INTO `sys_logininfor` VALUES (183, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:08:43');
INSERT INTO `sys_logininfor` VALUES (184, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:08:45');
INSERT INTO `sys_logininfor` VALUES (185, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:10:08');
INSERT INTO `sys_logininfor` VALUES (186, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:10:10');
INSERT INTO `sys_logininfor` VALUES (187, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:04');
INSERT INTO `sys_logininfor` VALUES (188, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:06');
INSERT INTO `sys_logininfor` VALUES (189, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:31');
INSERT INTO `sys_logininfor` VALUES (190, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:34');
INSERT INTO `sys_logininfor` VALUES (191, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:42');
INSERT INTO `sys_logininfor` VALUES (192, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:44');
INSERT INTO `sys_logininfor` VALUES (193, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:12:55');
INSERT INTO `sys_logininfor` VALUES (194, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:12:58');
INSERT INTO `sys_logininfor` VALUES (195, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:18');
INSERT INTO `sys_logininfor` VALUES (196, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:20');
INSERT INTO `sys_logininfor` VALUES (197, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:29');
INSERT INTO `sys_logininfor` VALUES (198, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:31');
INSERT INTO `sys_logininfor` VALUES (199, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:14:37');
INSERT INTO `sys_logininfor` VALUES (200, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:14:38');
INSERT INTO `sys_logininfor` VALUES (201, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:09');
INSERT INTO `sys_logininfor` VALUES (202, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:10');
INSERT INTO `sys_logininfor` VALUES (203, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:29');
INSERT INTO `sys_logininfor` VALUES (204, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:31');
INSERT INTO `sys_logininfor` VALUES (205, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:20:40');
INSERT INTO `sys_logininfor` VALUES (206, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:20:41');
INSERT INTO `sys_logininfor` VALUES (207, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-11 23:21:17');
INSERT INTO `sys_logininfor` VALUES (208, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:21:43');
INSERT INTO `sys_logininfor` VALUES (209, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:22:12');
INSERT INTO `sys_logininfor` VALUES (210, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:29:09');
INSERT INTO `sys_logininfor` VALUES (211, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-11 23:29:16');
INSERT INTO `sys_logininfor` VALUES (212, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-12 13:48:04');
INSERT INTO `sys_logininfor` VALUES (213, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '登录成功', '2025-07-12 13:54:54');
INSERT INTO `sys_logininfor` VALUES (214, 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Mac OS X (iPhone)', '0', '退出成功', '2025-07-12 13:54:57');
INSERT INTO `sys_logininfor` VALUES (215, 'user', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:04:10');
INSERT INTO `sys_logininfor` VALUES (216, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:04:16');
INSERT INTO `sys_logininfor` VALUES (217, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:05:22');
INSERT INTO `sys_logininfor` VALUES (218, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-23 00:05:29');
INSERT INTO `sys_logininfor` VALUES (219, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-07-23 00:05:35');
INSERT INTO `sys_logininfor` VALUES (220, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:05:40');
INSERT INTO `sys_logininfor` VALUES (221, 'admin_1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:08:57');
INSERT INTO `sys_logininfor` VALUES (222, 'admin_2', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:09:06');
INSERT INTO `sys_logininfor` VALUES (223, 'admin_2', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-07-23 00:09:36');
INSERT INTO `sys_logininfor` VALUES (224, 'admin_3', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-07-23 00:09:45');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(0) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(0) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int(0) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(0) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4049 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 99, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:59:02', '系统管理目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 1, '2025-06-07 19:23:02', 1, NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 1, '2025-06-07 19:23:02', 1, NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 1, '2025-06-07 19:23:02', 1, NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '1', 'system:dept:list', 'tree', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:52', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '1', 'system:post:list', 'post', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:45', '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '1', 'system:dict:list', 'dict', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:41', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 1, 10, 'job', 'system/job/index', '', '', 1, 0, 'C', '0', '1', 'monitor:job:list', 'job', 1, '2025-06-07 19:23:02', 1, '2025-07-22 23:58:37', '定时任务菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 1, '2025-06-07 19:23:02', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3000, '企业就业服务', 0, 1, 'policy', NULL, '', '', 1, 0, 'M', '0', '0', '', 'documentation', 1, '2025-07-21 00:00:00', 1, '2025-07-23 00:06:32', '政策管理目录');
INSERT INTO `sys_menu` VALUES (3001, '服务信息', 3000, 1, 'policy', 'policy/policy/index', '', '', 1, 0, 'C', '0', '0', 'policy:info:list', 'form', 1, '2025-07-21 00:00:00', 1, '2025-07-23 00:07:44', '政策信息菜单');
INSERT INTO `sys_menu` VALUES (3002, '服务审核', 3000, 2, 'application', 'policy/policyPlan/index', '', '', 1, 0, 'C', '0', '0', 'policy:application:list', 'edit', 1, '2025-07-21 00:00:00', 1, '2025-07-23 00:00:57', '政策申请菜单');
INSERT INTO `sys_menu` VALUES (3003, '培训订单', 4048, 3, 'order', 'order/index', '', '', 1, 0, 'C', '0', '0', 'training:order:list', 'skill', 1, '2025-07-22 00:00:00', 1, '2025-07-23 00:11:09', '培训订单菜单');
INSERT INTO `sys_menu` VALUES (3010, '政策信息查询', 3001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:query', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3011, '政策信息新增', 3001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:add', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3012, '政策信息修改', 3001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:edit', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3013, '政策信息删除', 3001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:remove', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3014, '政策信息导出', 3001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'policy:info:export', '#', 1, '2025-07-21 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3030, '培训订单查询', 3003, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:query', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3031, '培训订单新增', 3003, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:add', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3032, '培训订单修改', 3003, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:edit', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3033, '培训订单删除', 3003, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:remove', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3034, '培训订单导出', 3003, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:export', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3035, '培训订单发布', 3003, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:publish', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (3036, '培训订单取消', 3003, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'training:order:cancel', '#', 1, '2025-07-22 00:00:00', 1, NULL, '');
INSERT INTO `sys_menu` VALUES (4045, '初审', 3002, 0, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'policy:application:first-review', '#', 1, '2025-07-23 00:00:32', NULL, NULL, '');
INSERT INTO `sys_menu` VALUES (4046, '终审', 3002, 0, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'policy:application:final-review', '#', 1, '2025-07-23 00:00:44', NULL, NULL, '');
INSERT INTO `sys_menu` VALUES (4047, '企业就业服务申请', 0, 1, 'policyS', 'policy/policyS/index', NULL, '', 1, 0, 'M', '0', '0', '', 'button', 1, '2025-07-23 00:02:55', 1, '2025-07-23 00:06:17', '');
INSERT INTO `sys_menu` VALUES (4048, '就业培训管理', 0, 2, 'order', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'clipboard', 1, '2025-07-23 00:11:03', 1, '2025-07-23 00:11:13', '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 1, '2025-06-07 19:23:03', NULL, NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 1, '2025-06-07 19:23:03', NULL, NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(0) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(0) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(0) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(0) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 219 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1046', '127.0.0.1', '内网IP', '1046', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:41', 139);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1047', '127.0.0.1', '内网IP', '1047', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:49', 20);
INSERT INTO `sys_oper_log` VALUES (102, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1048', '127.0.0.1', '内网IP', '1048', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:50', 18);
INSERT INTO `sys_oper_log` VALUES (103, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1055', '127.0.0.1', '内网IP', '1055', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:52', 16);
INSERT INTO `sys_oper_log` VALUES (104, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1056', '127.0.0.1', '内网IP', '1056', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:53', 16);
INSERT INTO `sys_oper_log` VALUES (105, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1057', '127.0.0.1', '内网IP', '1057', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:54', 19);
INSERT INTO `sys_oper_log` VALUES (106, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1058', '127.0.0.1', '内网IP', '1058', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:56', 17);
INSERT INTO `sys_oper_log` VALUES (107, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1059', '127.0.0.1', '内网IP', '1059', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:58', 18);
INSERT INTO `sys_oper_log` VALUES (108, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1060', '127.0.0.1', '内网IP', '1060', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:25:59', 18);
INSERT INTO `sys_oper_log` VALUES (109, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/job/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"job\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":110,\"menuName\":\"定时任务\",\"menuType\":\"C\",\"orderNum\":10,\"params\":{},\"parentId\":1,\"path\":\"job\",\"perms\":\"monitor:job:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-07 19:26:10', 60);
INSERT INTO `sys_oper_log` VALUES (110, '参数管理', 2, 'com.sux.web.controller.system.SysConfigController.edit()', 'PUT', 1, 'admin', NULL, '/system/config', '127.0.0.1', '内网IP', '{\"configId\":3,\"configKey\":\"sys.index.sideTheme\",\"configName\":\"主框架页-侧边栏主题\",\"configType\":\"Y\",\"configValue\":\"theme-light\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"params\":{},\"remark\":\"深色主题theme-dark，浅色主题theme-light\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:40:21', 42);
INSERT INTO `sys_oper_log` VALUES (111, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"系统案例\",\"menuType\":\"C\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"1\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:44:10', 29);
INSERT INTO `sys_oper_log` VALUES (112, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/config/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":106,\"menuName\":\"参数设置\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":1,\"path\":\"config\",\"perms\":\"system:config:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:48:33', 23);
INSERT INTO `sys_oper_log` VALUES (113, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"使用案例\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"frame\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:52:56', 11);
INSERT INTO `sys_oper_log` VALUES (114, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"frame/vue-template/index\",\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"前端表单及组件集成\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"vue-template\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-26 20:54:24', 13);
INSERT INTO `sys_oper_log` VALUES (115, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2025/06/26/avatar_20250626210632A001.png\",\"code\":200}', 0, NULL, '2025-06-26 21:06:32', 333);
INSERT INTO `sys_oper_log` VALUES (116, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2025/06/26/avatar_20250626210638A002.png\",\"code\":200}', 0, NULL, '2025-06-26 21:06:38', 16);
INSERT INTO `sys_oper_log` VALUES (117, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】可爱-可爱猫-大眼猫_20250626224714A019.png\",\"code\":200}', 0, NULL, '2025-06-26 22:47:14', 22);
INSERT INTO `sys_oper_log` VALUES (118, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/06/26/【哲风壁纸】二次元场景-动漫插画_20250626224803A020.png\",\"code\":200}', 0, NULL, '2025-06-26 22:48:03', 9);
INSERT INTO `sys_oper_log` VALUES (119, '字典类型', 1, 'com.sux.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', NULL, '/system/dict/type', '127.0.0.1', '内网IP', '{\"createId\":1,\"dictName\":\"a\",\"dictType\":\"s\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:21:53', 118);
INSERT INTO `sys_oper_log` VALUES (120, '字典类型', 3, 'com.sux.web.controller.system.SysDictTypeController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/100', '127.0.0.1', '内网IP', '[100]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:21:56', 31);
INSERT INTO `sys_oper_log` VALUES (121, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"2\",\"nextValidTime\":\"2025-06-27 21:27:50\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:27:49', 26);
INSERT INTO `sys_oper_log` VALUES (122, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-27 21:28:00\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:27:53', 12);
INSERT INTO `sys_oper_log` VALUES (123, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.changeStatus()', 'PUT', 1, 'admin', NULL, '/system/role/changeStatus', '127.0.0.1', '内网IP', '{\"admin\":false,\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作失败\",\"code\":500}', 0, NULL, '2025-06-27 21:32:00', 12);
INSERT INTO `sys_oper_log` VALUES (124, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:34:38', 22);
INSERT INTO `sys_oper_log` VALUES (125, '字典类型', 9, 'com.sux.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/refreshCache', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:40:33', 25);
INSERT INTO `sys_oper_log` VALUES (126, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:41:29', 21);
INSERT INTO `sys_oper_log` VALUES (127, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/config/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":106,\"menuName\":\"参数设置\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":1,\"path\":\"config\",\"perms\":\"system:config:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-27 21:45:01', 13);
INSERT INTO `sys_oper_log` VALUES (128, '定时任务', 3, 'com.sux.quartz.controller.SysJobController.remove()', 'DELETE', 1, 'admin', NULL, '/monitor/job/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:55:11', 81);
INSERT INTO `sys_oper_log` VALUES (129, '定时任务', 3, 'com.sux.quartz.controller.SysJobController.remove()', 'DELETE', 1, 'admin', NULL, '/monitor/job/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:55:12', 20);
INSERT INTO `sys_oper_log` VALUES (130, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:58:28', 62);
INSERT INTO `sys_oper_log` VALUES (131, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 10:59:35', 55);
INSERT INTO `sys_oper_log` VALUES (132, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:05:19', 22);
INSERT INTO `sys_oper_log` VALUES (133, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:07:24', 23);
INSERT INTO `sys_oper_log` VALUES (134, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.run()', 'PUT', 1, 'admin', NULL, '/monitor/job/run', '127.0.0.1', '内网IP', '{\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"misfirePolicy\":\"0\",\"params\":{}}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 11:08:55', 24);
INSERT INTO `sys_oper_log` VALUES (135, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"0\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-28 13:29:10\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 13:29:05', 39);
INSERT INTO `sys_oper_log` VALUES (136, '定时任务', 2, 'com.sux.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', NULL, '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:03\",\"cronExpression\":\"0/10 * * * * ?\",\"invokeTarget\":\"ryTask.ryNoParams\",\"jobGroup\":\"DEFAULT\",\"jobId\":1,\"jobName\":\"系统默认（无参）\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-06-28 13:29:10\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 13:29:07', 8);
INSERT INTO `sys_oper_log` VALUES (137, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"vue-template/index\",\"createTime\":\"2025-06-26 20:54:24\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2001,\"menuName\":\"前端表单及组件集成\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"vue-template\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 14:15:06', 56);
INSERT INTO `sys_oper_log` VALUES (138, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[1],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 15:59:30', 215);
INSERT INTO `sys_oper_log` VALUES (139, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 15:59:34', 33);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-06-28 16:19:58', 6);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1030', '127.0.0.1', '内网IP', '1030', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:04', 14);
INSERT INTO `sys_oper_log` VALUES (142, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1031', '127.0.0.1', '内网IP', '1031', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:06', 12);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1032', '127.0.0.1', '内网IP', '1032', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:07', 14);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1033', '127.0.0.1', '内网IP', '1033', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:09', 11);
INSERT INTO `sys_oper_log` VALUES (145, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/1034', '127.0.0.1', '内网IP', '1034', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:11', 15);
INSERT INTO `sys_oper_log` VALUES (146, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:20:12', 13);
INSERT INTO `sys_oper_log` VALUES (147, '角色管理', 4, 'com.sux.web.controller.system.SysRoleController.selectAuthUserAll()', 'PUT', 1, 'admin', NULL, '/system/role/authUser/selectAll', '127.0.0.1', '内网IP', '{\"roleId\":\"2\",\"userIds\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:39:27', 20);
INSERT INTO `sys_oper_log` VALUES (148, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-06-07 19:23:02\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,2000,2001],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 16:39:37', 49);
INSERT INTO `sys_oper_log` VALUES (149, '用户管理', 5, 'com.sux.web.controller.system.SysUserController.export()', 'POST', 1, 'admin', NULL, '/system/user/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-28 16:41:57', 1461);
INSERT INTO `sys_oper_log` VALUES (150, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-26 20:52:56\",\"icon\":\"component\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2000,\"menuName\":\"使用案例\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"frame\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:01:00', 16);
INSERT INTO `sys_oper_log` VALUES (151, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:24', 16);
INSERT INTO `sys_oper_log` VALUES (152, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"1\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:31', 11);
INSERT INTO `sys_oper_log` VALUES (153, '岗位管理', 2, 'com.sux.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"flag\":false,\"params\":{},\"postCode\":\"ceo\",\"postId\":1,\"postName\":\"董事长\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:15:34', 7);
INSERT INTO `sys_oper_log` VALUES (154, '字典类型', 9, 'com.sux.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', NULL, '/system/dict/type/refreshCache', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-28 17:17:11', 35);
INSERT INTO `sys_oper_log` VALUES (155, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-06-07 19:23:02\",\"delFlag\":\"0\",\"dept\":{\"children\":[],\"deptId\":105,\"params\":{}},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2025-06-07 19:23:02\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[],\"pwdUpdateDate\":\"2025-06-07 19:23:02\",\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateId\":1,\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-29 03:56:41', 87);
INSERT INTO `sys_oper_log` VALUES (156, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/07/11/百相守梦(关羽)_20250711212529A001.png\",\"code\":200}', 0, NULL, '2025-07-11 21:25:30', 244);
INSERT INTO `sys_oper_log` VALUES (157, '用户头像', 2, 'com.sux.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/gitData/work/文件/avatar/2025/07/11/阿摩司公爵_20250711222932A001.png\",\"code\":200}', 0, NULL, '2025-07-11 22:29:32', 399);
INSERT INTO `sys_oper_log` VALUES (158, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"聊天\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"wechat\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:49:37', 22);
INSERT INTO `sys_oper_log` VALUES (159, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:49:45', 10);
INSERT INTO `sys_oper_log` VALUES (160, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"1\",\"createId\":1,\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"1\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:51:19', 16);
INSERT INTO `sys_oper_log` VALUES (161, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:52:58', 13);
INSERT INTO `sys_oper_log` VALUES (162, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/chat/private-chat/index\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:53:32', 9);
INSERT INTO `sys_oper_log` VALUES (163, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"chat/private-chat/index\",\"createTime\":\"2025-07-12 13:51:19\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"私聊\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"path\":\"privateChat\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-12 13:54:18', 11);
INSERT INTO `sys_oper_log` VALUES (164, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:55:35', 23);
INSERT INTO `sys_oper_log` VALUES (165, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3002', '127.0.0.1', '内网IP', '3002', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:56:29', 5);
INSERT INTO `sys_oper_log` VALUES (166, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3020', '127.0.0.1', '内网IP', '3020', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:33', 24);
INSERT INTO `sys_oper_log` VALUES (167, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3021', '127.0.0.1', '内网IP', '3021', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:35', 17);
INSERT INTO `sys_oper_log` VALUES (168, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3022', '127.0.0.1', '内网IP', '3022', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:37', 19);
INSERT INTO `sys_oper_log` VALUES (169, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3023', '127.0.0.1', '内网IP', '3023', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:38', 17);
INSERT INTO `sys_oper_log` VALUES (170, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3024', '127.0.0.1', '内网IP', '3024', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:40', 17);
INSERT INTO `sys_oper_log` VALUES (171, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3025', '127.0.0.1', '内网IP', '3025', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:41', 19);
INSERT INTO `sys_oper_log` VALUES (172, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3026', '127.0.0.1', '内网IP', '3026', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:43', 20);
INSERT INTO `sys_oper_log` VALUES (173, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3027', '127.0.0.1', '内网IP', '3027', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:44', 12);
INSERT INTO `sys_oper_log` VALUES (174, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/3028', '127.0.0.1', '内网IP', '3028', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:56:45', 13);
INSERT INTO `sys_oper_log` VALUES (175, '角色管理', 3, 'com.sux.web.controller.system.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/system/role/2', '127.0.0.1', '内网IP', '[2]', NULL, 1, '普通角色已分配,不能删除', '2025-07-22 23:57:53', 35);
INSERT INTO `sys_oper_log` VALUES (176, '角色管理', 4, 'com.sux.web.controller.system.SysRoleController.cancelAuthUserAll()', 'PUT', 1, 'admin', NULL, '/system/role/authUser/cancelAll', '127.0.0.1', '内网IP', '{\"roleId\":\"2\",\"userIds\":\"2,1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:01', 25);
INSERT INTO `sys_oper_log` VALUES (177, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:58:10', 6);
INSERT INTO `sys_oper_log` VALUES (178, '角色管理', 3, 'com.sux.web.controller.system.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/system/role/2', '127.0.0.1', '内网IP', '[2]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:15', 35);
INSERT INTO `sys_oper_log` VALUES (179, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2001', '127.0.0.1', '内网IP', '2001', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:19', 15);
INSERT INTO `sys_oper_log` VALUES (180, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/2000', '127.0.0.1', '内网IP', '2000', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:21', 24);
INSERT INTO `sys_oper_log` VALUES (181, '菜单管理', 3, 'com.sux.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/4000', '127.0.0.1', '内网IP', '4000', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-07-22 23:58:26', 5);
INSERT INTO `sys_oper_log` VALUES (182, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/job/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"job\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":110,\"menuName\":\"定时任务\",\"menuType\":\"C\",\"orderNum\":10,\"params\":{},\"parentId\":1,\"path\":\"job\",\"perms\":\"monitor:job:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:37', 42);
INSERT INTO `sys_oper_log` VALUES (183, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/dict/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":105,\"menuName\":\"字典管理\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":1,\"path\":\"dict\",\"perms\":\"system:dict:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:41', 14);
INSERT INTO `sys_oper_log` VALUES (184, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/post/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"post\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":104,\"menuName\":\"岗位管理\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":1,\"path\":\"post\",\"perms\":\"system:post:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:45', 15);
INSERT INTO `sys_oper_log` VALUES (185, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"system/dept/index\",\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":103,\"menuName\":\"部门管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1,\"path\":\"dept\",\"perms\":\"system:dept:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"1\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:58:52', 15);
INSERT INTO `sys_oper_log` VALUES (186, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-06-07 19:23:02\",\"icon\":\"system\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1,\"menuName\":\"系统管理\",\"menuType\":\"M\",\"orderNum\":99,\"params\":{},\"parentId\":0,\"path\":\"system\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:02', 14);
INSERT INTO `sys_oper_log` VALUES (187, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:20', 15);
INSERT INTO `sys_oper_log` VALUES (188, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:33', 15);
INSERT INTO `sys_oper_log` VALUES (189, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyPlan/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3002,\"menuName\":\"政策审核\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3000,\"path\":\"application\",\"perms\":\"policy:application:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-22 23:59:52', 12);
INSERT INTO `sys_oper_log` VALUES (190, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"初审\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":3002,\"perms\":\"policy:application:first-review\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:33', 12);
INSERT INTO `sys_oper_log` VALUES (191, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"终审\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":3002,\"perms\":\"policy:application:final-review\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:44', 9);
INSERT INTO `sys_oper_log` VALUES (192, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/info/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"info\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:52', 11);
INSERT INTO `sys_oper_log` VALUES (193, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyPlan/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3002,\"menuName\":\"服务审核\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3000,\"path\":\"application\",\"perms\":\"policy:application:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:00:57', 14);
INSERT INTO `sys_oper_log` VALUES (194, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4046],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:01:37', 40);
INSERT INTO `sys_oper_log` VALUES (195, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4045],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:01:59', 11);
INSERT INTO `sys_oper_log` VALUES (196, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,4046],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:02:16', 24);
INSERT INTO `sys_oper_log` VALUES (197, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyS/index\",\"createId\":1,\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"企业就业服务申请\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policyS\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:02:55', 10);
INSERT INTO `sys_oper_log` VALUES (198, '角色管理', 1, 'com.sux.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"dataScope\":\"1\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[4047],\"params\":{},\"roleId\":102,\"roleKey\":\"企业就业\",\"roleName\":\"企业就业服务申请\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:18', 15);
INSERT INTO `sys_oper_log` VALUES (199, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_1\",\"params\":{},\"postIds\":[],\"roleIds\":[],\"status\":\"0\",\"userId\":100,\"userName\":\"admin_1\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:41', 164);
INSERT INTO `sys_oper_log` VALUES (200, '用户管理', 2, 'com.sux.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createId\":1,\"createTime\":\"2025-07-23 00:03:41\",\"delFlag\":\"0\",\"email\":\"\",\"loginIp\":\"\",\"nickName\":\"admin_1\",\"params\":{},\"phonenumber\":\"\",\"postIds\":[],\"roleIds\":[100],\"roles\":[],\"sex\":\"0\",\"status\":\"0\",\"updateId\":1,\"userId\":100,\"userName\":\"admin_1\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:45', 27);
INSERT INTO `sys_oper_log` VALUES (201, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_2\",\"params\":{},\"postIds\":[],\"roleIds\":[101],\"status\":\"0\",\"userId\":101,\"userName\":\"admin_2\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:03:55', 126);
INSERT INTO `sys_oper_log` VALUES (202, '用户管理', 1, 'com.sux.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createId\":1,\"email\":\"\",\"nickName\":\"admin_3\",\"params\":{},\"postIds\":[],\"roleIds\":[102],\"status\":\"0\",\"userId\":102,\"userName\":\"admin_3\",\"userType\":\"00\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:04:03', 149);
INSERT INTO `sys_oper_log` VALUES (203, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_3', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:04:28', 506);
INSERT INTO `sys_oper_log` VALUES (204, '政策申请', 1, 'com.sux.web.controller.policy.PolicyApplicationController.add()', 'POST', 1, 'admin_3', NULL, '/policy/application', '127.0.0.1', '内网IP', '{\"applicantName\":\"张三\",\"applicantPhone\":\"***********\",\"applicantUserId\":102,\"applicationId\":2,\"applicationStatus\":\"0\",\"createId\":102,\"createTime\":\"2025-07-23 00:05:14\",\"params\":{},\"policyId\":1,\"remark\":\"\",\"requiredMaterials\":\"[{\\\"name\\\":\\\"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】二次元场景-动漫插画.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】二次元场景-动漫插画_20250723000455A001.png\\\",\\\"uid\\\":*************,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业的营业执照副本复印件\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000457A002.png\\\",\\\"uid\\\":*************,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"退役军人需提供退伍证原件及复印件\\\",\\\"required\\\":false,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000459A003.png\\\",\\\"uid\\\":1753200299883,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业社保缴费凭证\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000502A004.png\\\",\\\"uid\\\":1753200302121,\\\"status\\\":\\\"success\\\"}]},{\\\"name\\\":\\\"企业在银行开立的基本账户\\\",\\\"required\\\":true,\\\"files\\\":[{\\\"name\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\\\",\\\"sourceFileName\\\":\\\"【哲风壁纸】可爱-可爱猫-大眼猫.png\\\",\\\"filePath\\\":\\\"/gitData/work/文件/upload/2025/07/23/【哲风壁纸】可爱-可爱猫-大眼猫_20250723000503A005.png\\\",\\\"uid\\\":1753200303955,\\\"status\\\":\\\"success\\\"}]}]\",\"submitTime\":\"2025-07-23 00:05:14\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:05:14', 33);
INSERT INTO `sys_oper_log` VALUES (205, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_1', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:05:48', 487);
INSERT INTO `sys_oper_log` VALUES (206, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policyS/index\",\"createTime\":\"2025-07-23 00:02:55\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4047,\"menuName\":\"企业就业服务申请\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policyS\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:17', 15);
INSERT INTO `sys_oper_log` VALUES (207, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3000,\"menuName\":\"企业就业服务\",\"menuType\":\"M\",\"orderNum\":1,\"params\":{},\"parentId\":0,\"path\":\"policy\",\"perms\":\"\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:32', 9);
INSERT INTO `sys_oper_log` VALUES (208, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:59\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4046],\"params\":{},\"roleId\":101,\"roleKey\":\"终审\",\"roleName\":\"企业就业服务终审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:06:55', 19);
INSERT INTO `sys_oper_log` VALUES (209, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:37\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4046],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:04', 21);
INSERT INTO `sys_oper_log` VALUES (210, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/info/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"policyinfo\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:29', 11);
INSERT INTO `sys_oper_log` VALUES (211, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"policy/policy/index\",\"createTime\":\"2025-07-21 00:00:00\",\"icon\":\"form\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3001,\"menuName\":\"服务信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":3000,\"path\":\"policy\",\"perms\":\"policy:info:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:07:44', 16);
INSERT INTO `sys_oper_log` VALUES (212, '角色管理', 2, 'com.sux.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2025-07-23 00:01:37\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3000,3002,3001,3010,3011,3012,3013,3014,4045],\"params\":{},\"roleId\":100,\"roleKey\":\"初审\",\"roleName\":\"企业就业服务初审\",\"roleSort\":0,\"status\":\"0\",\"updateId\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:08:40', 21);
INSERT INTO `sys_oper_log` VALUES (213, '政策申请初审', 2, 'com.sux.web.controller.policy.PolicyApplicationController.firstReview()', 'POST', 1, 'admin_1', NULL, '/policy/application/first-review', '127.0.0.1', '内网IP', '{\"applicationId\":1,\"approvalStatus\":\"1\",\"approvalComment\":\"ces \"}', '{\"msg\":\"初审通过成功\",\"code\":200}', 0, NULL, '2025-07-23 00:08:53', 20);
INSERT INTO `sys_oper_log` VALUES (214, '个人信息', 2, 'com.sux.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin_2', NULL, '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:09:15', 463);
INSERT INTO `sys_oper_log` VALUES (215, '政策申请终审', 2, 'com.sux.web.controller.policy.PolicyApplicationController.finalReview()', 'POST', 1, 'admin_2', NULL, '/policy/application/final-review', '127.0.0.1', '内网IP', '{\"applicationId\":1,\"approvalStatus\":\"2\",\"approvalComment\":\"ceshi \"}', '{\"msg\":\"终审拒绝成功\",\"code\":200}', 0, NULL, '2025-07-23 00:09:25', 12);
INSERT INTO `sys_oper_log` VALUES (216, '菜单管理', 1, 'com.sux.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createId\":1,\"icon\":\"clipboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"就业培训管理\",\"menuType\":\"M\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"order\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:03', 9);
INSERT INTO `sys_oper_log` VALUES (217, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"order/index\",\"createTime\":\"2025-07-22 00:00:00\",\"icon\":\"skill\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":3003,\"menuName\":\"培训订单\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":4048,\"path\":\"order\",\"perms\":\"training:order:list\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:09', 13);
INSERT INTO `sys_oper_log` VALUES (218, '菜单管理', 2, 'com.sux.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"createTime\":\"2025-07-23 00:11:03\",\"icon\":\"clipboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":4048,\"menuName\":\"就业培训管理\",\"menuType\":\"M\",\"orderNum\":2,\"params\":{},\"parentId\":0,\"path\":\"order\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateId\":1,\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-23 00:11:13', 9);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(0) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 1, '2025-06-07 19:23:02', 1, '2025-06-28 17:15:34', '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(0) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 1, '2025-06-07 19:23:02', NULL, NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '2', 1, '2025-06-07 19:23:02', 1, '2025-06-28 16:39:37', '普通角色');
INSERT INTO `sys_role` VALUES (100, '企业就业服务初审', '初审', 0, '1', 1, 1, '0', '0', 1, '2025-07-23 00:01:37', 1, '2025-07-23 00:08:40', NULL);
INSERT INTO `sys_role` VALUES (101, '企业就业服务终审', '终审', 0, '1', 1, 1, '0', '0', 1, '2025-07-23 00:01:59', 1, '2025-07-23 00:06:55', NULL);
INSERT INTO `sys_role` VALUES (102, '企业就业服务申请', '企业就业', 0, '1', 1, 1, '0', '0', 1, '2025-07-23 00:03:18', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(0) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(0) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (100, 3000);
INSERT INTO `sys_role_menu` VALUES (100, 3001);
INSERT INTO `sys_role_menu` VALUES (100, 3002);
INSERT INTO `sys_role_menu` VALUES (100, 3010);
INSERT INTO `sys_role_menu` VALUES (100, 3011);
INSERT INTO `sys_role_menu` VALUES (100, 3012);
INSERT INTO `sys_role_menu` VALUES (100, 3013);
INSERT INTO `sys_role_menu` VALUES (100, 3014);
INSERT INTO `sys_role_menu` VALUES (100, 4045);
INSERT INTO `sys_role_menu` VALUES (101, 3000);
INSERT INTO `sys_role_menu` VALUES (101, 3001);
INSERT INTO `sys_role_menu` VALUES (101, 3002);
INSERT INTO `sys_role_menu` VALUES (101, 3010);
INSERT INTO `sys_role_menu` VALUES (101, 3011);
INSERT INTO `sys_role_menu` VALUES (101, 3012);
INSERT INTO `sys_role_menu` VALUES (101, 3013);
INSERT INTO `sys_role_menu` VALUES (101, 3014);
INSERT INTO `sys_role_menu` VALUES (101, 4046);
INSERT INTO `sys_role_menu` VALUES (102, 4047);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(0) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `pwd_update_date` datetime(0) NULL DEFAULT NULL COMMENT '密码最后更新时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '若依', '00', '<EMAIL>', '15888888888', '1', '/gitData/work/文件/avatar/2025/07/11/阿摩司公爵_20250711222932A001.png', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-07-12 13:54:54', '2025-06-07 19:23:02', 1, '2025-06-07 19:23:02', NULL, '2025-07-12 13:54:54', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-06-07 19:23:02', '2025-06-07 19:23:02', 1, '2025-06-07 19:23:02', 1, '2025-06-29 03:56:41', '测试员');
INSERT INTO `sys_user` VALUES (100, NULL, 'admin_1', 'admin_1', '00', '', '', '0', '', '$2a$10$Yl0fUdUbt2Lg4HpUK0YE0ua8wJgtgXtxjkSyZ.dX2lyvdoxqLC/aq', '0', '0', '127.0.0.1', '2025-07-23 00:05:40', '2025-07-23 00:05:48', 1, '2025-07-23 00:03:41', 1, '2025-07-23 00:05:40', NULL);
INSERT INTO `sys_user` VALUES (101, NULL, 'admin_2', 'admin_2', '00', '', '', '0', '', '$2a$10$SxxQ5kGq/MQY13i4Ut8C9e2C7V4.pFtEUYwrRhnPPNnLEkswfAPES', '0', '0', '127.0.0.1', '2025-07-23 00:09:07', '2025-07-23 00:09:15', 1, '2025-07-23 00:03:55', NULL, '2025-07-23 00:09:06', NULL);
INSERT INTO `sys_user` VALUES (102, NULL, 'admin_3', 'admin_3', '00', '', '', '0', '', '$2a$10$.8RtCTmKu7V8u5bixgplPulef2QC9d81KpffBW8U1usgkp7st2fye', '0', '0', '127.0.0.1', '2025-07-23 00:09:46', '2025-07-23 00:04:28', 1, '2025-07-23 00:04:03', NULL, '2025-07-23 00:09:45', NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `post_id` bigint(0) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (100, 100);
INSERT INTO `sys_user_role` VALUES (101, 101);
INSERT INTO `sys_user_role` VALUES (102, 102);

-- ----------------------------
-- Table structure for training_order
-- ----------------------------
DROP TABLE IF EXISTS `training_order`;
CREATE TABLE `training_order`  (
  `order_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单标题',
  `order_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '订单描述',
  `training_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训类型',
  `training_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训分类',
  `training_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训级别',
  `training_duration` int(0) NULL DEFAULT NULL COMMENT '培训时长(小时)',
  `max_participants` int(0) NULL DEFAULT NULL COMMENT '最大参与人数',
  `current_participants` int(0) NULL DEFAULT 0 COMMENT '当前报名人数',
  `training_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '培训费用',
  `training_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训地址',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `start_date` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_date` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `registration_deadline` datetime(0) NULL DEFAULT NULL COMMENT '报名截止时间',
  `order_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '订单状态（0草稿 1发布 2进行中 3已完成 4已取消）',
  `is_featured` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '是否推荐（0否 1是）',
  `requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '报名要求',
  `certificate_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '证书信息',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`order_id`) USING BTREE,
  INDEX `idx_training_type`(`training_type`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE,
  INDEX `idx_start_date`(`start_date`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_order
-- ----------------------------
INSERT INTO `training_order` VALUES (1, 'Java高级开发工程师培训', '面向有一定Java基础的开发人员，深入学习Spring Boot、微服务架构、分布式系统等高级技术', '技术培训', 'IT技能', '高级', 120, 30, 0, 5800.00, '青岛市市南区香港中路10号颐和国际A座15楼', '张老师', '13800138001', '<EMAIL>', '2025-08-01 09:00:00', '2025-08-15 18:00:00', '2025-07-28 23:59:59', '1', '1', '要求具备2年以上Java开发经验，熟悉Spring框架基础知识', '完成培训可获得高级Java开发工程师认证证书', '0', 1, '2025-07-22 10:00:00', NULL, '2025-07-22 10:00:00', '企业内训项目');
INSERT INTO `training_order` VALUES (2, 'Python数据分析师培训', '零基础学习Python数据分析，包括pandas、numpy、matplotlib等核心库的使用', '技术培训', 'IT技能', '初级', 80, 25, 0, 3800.00, '青岛市崂山区海尔路178号', '李老师', '13800138002', '<EMAIL>', '2025-08-05 09:00:00', '2025-08-12 18:00:00', '2025-08-02 23:59:59', '1', '0', '无编程基础要求，适合零基础学员', '完成培训可获得Python数据分析师认证证书', '0', 1, '2025-07-22 10:30:00', NULL, '2025-07-22 10:30:00', '面向社会招生');
INSERT INTO `training_order` VALUES (3, '企业管理与领导力提升', '针对中高层管理人员的综合管理能力提升培训，涵盖团队管理、战略规划、沟通技巧等', '管理培训', '领导力', '中级', 40, 20, 12, 4500.00, '青岛市市北区辽宁路263号', '王老师', '13800138003', '<EMAIL>', '2025-07-30 09:00:00', '2025-08-01 18:00:00', '2025-07-25 23:59:59', '1', '1', '具备3年以上管理经验或相关工作背景', '完成培训可获得企业管理师认证证书', '0', 1, '2025-07-22 11:00:00', NULL, '2025-07-22 11:00:00', '管理技能提升');
INSERT INTO `training_order` VALUES (4, '数字营销与电商运营', '全面学习数字营销策略、社交媒体运营、电商平台操作等现代营销技能', '职业技能', '市场营销', '中级', 60, 35, 22, 0.00, '青岛市黄岛区长江中路216号', '刘老师', '13800138004', '<EMAIL>', '2025-08-10 09:00:00', '2025-08-17 18:00:00', '2025-08-05 23:59:59', '1', '0', '对电商或营销有基础了解，有相关工作经验优先', '完成培训可获得数字营销师认证证书', '0', 1, '2025-07-22 11:30:00', NULL, '2025-07-22 11:30:00', '免费公益培训');
INSERT INTO `training_order` VALUES (5, '前端开发技能提升', '学习最新的前端开发技术，包括Vue3、React、TypeScript等现代前端框架', '技术培训', 'IT技能', '中级', 100, 28, 18, 4200.00, '青岛市市南区东海西路15号', '陈老师', '13800138005', '<EMAIL>', '2025-08-12 09:00:00', '2025-08-20 18:00:00', '2025-08-08 23:59:59', '1', '1', '具备HTML、CSS、JavaScript基础知识', '完成培训可获得前端开发工程师认证证书', '0', 1, '2025-07-22 12:00:00', NULL, '2025-07-22 12:00:00', '前端技术进阶');
INSERT INTO `training_order` VALUES (6, '项目管理PMP认证培训', '系统学习项目管理知识体系，准备PMP认证考试，提升项目管理专业能力', '管理培训', '项目管理', '高级', 80, 15, 8, 6800.00, '青岛市崂山区科技街36号', '赵老师', '13800138006', '<EMAIL>', '2025-08-15 09:00:00', '2025-08-25 18:00:00', '2025-08-10 23:59:59', '1', '1', '具备3年以上项目管理经验，大学本科以上学历', '完成培训可获得PMP认证培训证书', '0', 1, '2025-07-22 12:30:00', NULL, '2025-07-22 12:30:00', 'PMP认证考试培训');
INSERT INTO `training_order` VALUES (7, '财务分析与预算管理', '深入学习财务分析方法、预算编制与控制、成本管理等财务管理核心技能', '管理培训', '财务管理', '中级', 50, 25, 15, 3500.00, '青岛市市南区山东路9号', '孙老师', '13800138007', '<EMAIL>', '2025-08-18 09:00:00', '2025-08-22 18:00:00', '2025-08-12 23:59:59', '1', '0', '具备财务基础知识，有一定工作经验', '完成培训可获得财务分析师认证证书', '0', 1, '2025-07-22 13:00:00', NULL, '2025-07-22 13:00:00', '财务技能提升');
INSERT INTO `training_order` VALUES (8, '人工智能与机器学习入门', '零基础学习人工智能和机器学习基础知识，包括Python编程、数据处理、算法原理等', '技术培训', 'IT技能', '初级', 120, 40, 25, 5200.00, '青岛市高新区智力岛路1号', '周老师', '13800138008', '<EMAIL>', '2025-08-20 09:00:00', '2025-09-05 18:00:00', '2025-08-15 23:59:59', '1', '1', '理工科背景，具备基础数学知识', '完成培训可获得AI工程师入门认证证书', '0', 1, '2025-07-22 13:30:00', NULL, '2025-07-22 13:30:00', 'AI技术入门');

-- ----------------------------
-- Table structure for training_registration
-- ----------------------------
DROP TABLE IF EXISTS `training_registration`;
CREATE TABLE `training_registration`  (
  `registration_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `order_id` bigint(0) NOT NULL COMMENT '订单ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `participant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者姓名',
  `participant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参与者手机号',
  `participant_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者邮箱',
  `participant_company` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者公司',
  `participant_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参与者职位',
  `registration_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '报名状态（0待审核 1已通过 2已拒绝 3已取消）',
  `registration_time` datetime(0) NULL DEFAULT NULL COMMENT '报名时间',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint(0) NULL DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`registration_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_registration_status`(`registration_status`) USING BTREE,
  INDEX `idx_registration_time`(`registration_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '培训报名表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of training_registration
-- ----------------------------

-- ----------------------------
-- Table structure for worker_info
-- ----------------------------
DROP TABLE IF EXISTS `worker_info`;
CREATE TABLE `worker_info`  (
  `worker_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `gender` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别(0男 1女)',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号码',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前所在地',
  `work_location_preference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作地点偏好(JSON格式)',
  `education_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历水平',
  `work_experience_years` int(0) NULL DEFAULT 0 COMMENT '工作经验年数',
  `work_experience_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作经验描述',
  `expected_salary_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最低薪资',
  `expected_salary_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最高薪资',
  `salary_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'month' COMMENT '薪资单位(hour/day/month)',
  `work_type_preference` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作类型偏好',
  `available_start_date` date NULL DEFAULT NULL COMMENT '可开始工作日期',
  `available_end_date` date NULL DEFAULT NULL COMMENT '可工作截止日期',
  `work_time_preference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作时间偏好(JSON格式)',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `resume_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '简历文件',
  `certificate_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '证书文件(JSON格式)',
  `is_available` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否可接单(0否 1是)',
  `rating_score` decimal(3, 2) NULL DEFAULT 5.00 COMMENT '评分(1-5分)',
  `completed_jobs` int(0) NULL DEFAULT 0 COMMENT '完成工作数量',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE,
  INDEX `idx_current_location`(`current_location`) USING BTREE,
  INDEX `idx_education_level`(`education_level`) USING BTREE,
  INDEX `idx_is_available`(`is_available`) USING BTREE,
  INDEX `idx_rating_score`(`rating_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_info
-- ----------------------------

-- ----------------------------
-- Table structure for worker_profile
-- ----------------------------
DROP TABLE IF EXISTS `worker_profile`;
CREATE TABLE `worker_profile`  (
  `worker_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别（male/female）',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `age` int(0) NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `wechat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信号',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前所在地',
  `current_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
  `work_locations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '可工作地点（JSON数组）',
  `education_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历水平',
  `work_experience_years` int(0) NULL DEFAULT NULL COMMENT '工作经验年数',
  `work_categories` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作类别偏好（JSON数组）',
  `job_types_preferred` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '偏好工作类型（JSON数组）',
  `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能列表（JSON格式）',
  `certifications` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '证书资质（JSON格式）',
  `languages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '语言能力（JSON格式）',
  `work_time_preference` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作时间偏好（flexible/fixed/part_time/full_time）',
  `salary_expectation_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最低薪资',
  `salary_expectation_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最高薪资',
  `salary_type_preference` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '薪资类型偏好（hourly/daily/monthly/piece）',
  `availability_start_date` date NULL DEFAULT NULL COMMENT '可开始工作日期',
  `availability_end_date` date NULL DEFAULT NULL COMMENT '可工作截止日期',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周可工作天数',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日可工作小时数',
  `physical_condition` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身体状况',
  `health_certificate` tinyint(1) NULL DEFAULT 0 COMMENT '是否有健康证（0否 1是）',
  `criminal_record_check` tinyint(1) NULL DEFAULT 0 COMMENT '是否通过无犯罪记录检查（0否 1是）',
  `emergency_contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人姓名',
  `emergency_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人电话',
  `emergency_contact_relation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人关系',
  `profile_photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像照片URL',
  `resume_file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '简历文件URL',
  `portfolio_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '作品集文件URLs（JSON数组）',
  `work_history` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作履历（JSON格式）',
  `references` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '推荐人信息（JSON格式）',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `special_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '特殊说明',
  `rating_average` decimal(3, 2) NULL COMMENT '平均评分',
  `rating_count` int(0) NULL DEFAULT 0 COMMENT '评分次数',
  `completed_jobs` int(0) NULL DEFAULT 0 COMMENT '完成工作数量',
  `success_rate` decimal(5, 2) NULL COMMENT '成功率',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active' COMMENT '状态（active/inactive/suspended/banned）',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已实名验证（0否 1是）',
  `verification_time` datetime(0) NULL DEFAULT NULL COMMENT '验证时间',
  `last_active_time` datetime(0) NULL DEFAULT NULL COMMENT '最后活跃时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE,
  INDEX `idx_real_name`(`real_name`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_current_location`(`current_location`) USING BTREE,
  INDEX `idx_education_level`(`education_level`) USING BTREE,
  INDEX `idx_work_experience`(`work_experience_years`) USING BTREE,
  INDEX `idx_salary_expectation`(`salary_expectation_min`, `salary_expectation_max`) USING BTREE,
  INDEX `idx_availability`(`availability_start_date`, `availability_end_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_rating`(`rating_average`) USING BTREE,
  INDEX `idx_verified`(`is_verified`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_profile
-- ----------------------------

-- ----------------------------
-- Table structure for worker_skill
-- ----------------------------
DROP TABLE IF EXISTS `worker_skill`;
CREATE TABLE `worker_skill`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `worker_id` bigint(0) NOT NULL COMMENT '零工ID',
  `skill_id` bigint(0) NOT NULL COMMENT '技能ID',
  `skill_level` tinyint(0) NULL DEFAULT 1 COMMENT '技能等级(1-初级 2-中级 3-高级)',
  `experience_years` int(0) NULL DEFAULT 0 COMMENT '该技能经验年数',
  `certificate_file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '技能证书文件',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_worker_skill`(`worker_id`, `skill_id`) USING BTREE,
  INDEX `idx_worker_id`(`worker_id`) USING BTREE,
  INDEX `idx_skill_id`(`skill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工技能关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_skill
-- ----------------------------

-- ----------------------------
-- View structure for v_match_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_match_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_match_detail` AS select `m`.`match_id` AS `match_id`,`m`.`recruitment_id` AS `recruitment_id`,`m`.`worker_id` AS `worker_id`,`m`.`match_score` AS `match_score`,`m`.`skill_match_score` AS `skill_match_score`,`m`.`location_match_score` AS `location_match_score`,`m`.`salary_match_score` AS `salary_match_score`,`m`.`experience_match_score` AS `experience_match_score`,`m`.`education_match_score` AS `education_match_score`,`m`.`time_match_score` AS `time_match_score`,`m`.`match_details` AS `match_details`,`m`.`match_status` AS `match_status`,`m`.`is_mutual_match` AS `is_mutual_match`,`m`.`employer_viewed` AS `employer_viewed`,`m`.`worker_viewed` AS `worker_viewed`,`m`.`match_time` AS `match_time`,`m`.`create_id` AS `create_id`,`m`.`create_time` AS `create_time`,`m`.`update_id` AS `update_id`,`m`.`update_time` AS `update_time`,`m`.`remark` AS `remark`,`r`.`job_title` AS `job_title`,`r`.`company_name` AS `company_name`,`r`.`work_location` AS `job_location`,`r`.`salary_min` AS `salary_min`,`r`.`salary_max` AS `salary_max`,`r`.`salary_unit` AS `salary_unit`,`w`.`real_name` AS `worker_name`,`w`.`phone_number` AS `worker_phone`,`w`.`current_location` AS `worker_location`,`w`.`rating_score` AS `worker_rating`,`u1`.`nick_name` AS `employer_name`,`u2`.`nick_name` AS `worker_user_name` from ((((`match_record` `m` left join `recruitment_info` `r` on((`m`.`recruitment_id` = `r`.`recruitment_id`))) left join `worker_info` `w` on((`m`.`worker_id` = `w`.`worker_id`))) left join `sys_user` `u1` on((`r`.`publisher_user_id` = `u1`.`user_id`))) left join `sys_user` `u2` on((`w`.`user_id` = `u2`.`user_id`))) where ((`r`.`del_flag` = '0') and (`w`.`del_flag` = '0'));

-- ----------------------------
-- View structure for v_recruitment_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_recruitment_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_recruitment_detail` AS select `r`.`recruitment_id` AS `recruitment_id`,`r`.`job_title` AS `job_title`,`r`.`company_name` AS `company_name`,`r`.`job_description` AS `job_description`,`r`.`job_requirements` AS `job_requirements`,`r`.`work_location` AS `work_location`,`r`.`work_type` AS `work_type`,`r`.`salary_min` AS `salary_min`,`r`.`salary_max` AS `salary_max`,`r`.`salary_unit` AS `salary_unit`,`r`.`education_requirement` AS `education_requirement`,`r`.`experience_requirement` AS `experience_requirement`,`r`.`age_min` AS `age_min`,`r`.`age_max` AS `age_max`,`r`.`gender_requirement` AS `gender_requirement`,`r`.`contact_person` AS `contact_person`,`r`.`contact_phone` AS `contact_phone`,`r`.`contact_email` AS `contact_email`,`r`.`recruitment_count` AS `recruitment_count`,`r`.`current_applicants` AS `current_applicants`,`r`.`work_start_date` AS `work_start_date`,`r`.`work_end_date` AS `work_end_date`,`r`.`application_deadline` AS `application_deadline`,`r`.`is_urgent` AS `is_urgent`,`r`.`is_featured` AS `is_featured`,`r`.`status` AS `status`,`r`.`publisher_user_id` AS `publisher_user_id`,`r`.`company_logo` AS `company_logo`,`r`.`job_images` AS `job_images`,`r`.`del_flag` AS `del_flag`,`r`.`create_id` AS `create_id`,`r`.`create_time` AS `create_time`,`r`.`update_id` AS `update_id`,`r`.`update_time` AS `update_time`,`r`.`remark` AS `remark`,`u`.`nick_name` AS `publisher_name`,`u`.`phonenumber` AS `publisher_phone`,group_concat(concat(`st`.`skill_name`,'(',`rs`.`skill_level_required`,')') separator ', ') AS `required_skills` from (((`recruitment_info` `r` left join `sys_user` `u` on((`r`.`publisher_user_id` = `u`.`user_id`))) left join `recruitment_skill` `rs` on((`r`.`recruitment_id` = `rs`.`recruitment_id`))) left join `skill_tag` `st` on((`rs`.`skill_id` = `st`.`skill_id`))) where (`r`.`del_flag` = '0') group by `r`.`recruitment_id`;

-- ----------------------------
-- View structure for v_worker_detail
-- ----------------------------
DROP VIEW IF EXISTS `v_worker_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_worker_detail` AS select `w`.`worker_id` AS `worker_id`,`w`.`user_id` AS `user_id`,`w`.`real_name` AS `real_name`,`w`.`gender` AS `gender`,`w`.`birth_date` AS `birth_date`,`w`.`id_card` AS `id_card`,`w`.`phone_number` AS `phone_number`,`w`.`email` AS `email`,`w`.`current_location` AS `current_location`,`w`.`work_location_preference` AS `work_location_preference`,`w`.`education_level` AS `education_level`,`w`.`work_experience_years` AS `work_experience_years`,`w`.`work_experience_desc` AS `work_experience_desc`,`w`.`expected_salary_min` AS `expected_salary_min`,`w`.`expected_salary_max` AS `expected_salary_max`,`w`.`salary_unit` AS `salary_unit`,`w`.`work_type_preference` AS `work_type_preference`,`w`.`available_start_date` AS `available_start_date`,`w`.`available_end_date` AS `available_end_date`,`w`.`work_time_preference` AS `work_time_preference`,`w`.`self_introduction` AS `self_introduction`,`w`.`avatar` AS `avatar`,`w`.`resume_file` AS `resume_file`,`w`.`certificate_files` AS `certificate_files`,`w`.`is_available` AS `is_available`,`w`.`rating_score` AS `rating_score`,`w`.`completed_jobs` AS `completed_jobs`,`w`.`status` AS `status`,`w`.`del_flag` AS `del_flag`,`w`.`create_id` AS `create_id`,`w`.`create_time` AS `create_time`,`w`.`update_id` AS `update_id`,`w`.`update_time` AS `update_time`,`w`.`remark` AS `remark`,`u`.`nick_name` AS `user_name`,`u`.`email` AS `user_email`,group_concat(concat(`st`.`skill_name`,'(',`ws`.`skill_level`,')') separator ', ') AS `worker_skills`,timestampdiff(YEAR,`w`.`birth_date`,curdate()) AS `age` from (((`worker_info` `w` left join `sys_user` `u` on((`w`.`user_id` = `u`.`user_id`))) left join `worker_skill` `ws` on((`w`.`worker_id` = `ws`.`worker_id`))) left join `skill_tag` `st` on((`ws`.`skill_id` = `st`.`skill_id`))) where (`w`.`del_flag` = '0') group by `w`.`worker_id`;

-- ----------------------------
-- Procedure structure for sp_calculate_match_score
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_calculate_match_score`;
delimiter ;;
CREATE PROCEDURE `sp_calculate_match_score`(IN p_recruitment_id BIGINT,
    IN p_worker_id BIGINT,
    OUT p_match_score DECIMAL(5,2))
BEGIN
    DECLARE v_skill_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_location_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_salary_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_experience_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_education_score DECIMAL(5,2) DEFAULT 0;
    DECLARE v_time_score DECIMAL(5,2) DEFAULT 0;

    -- 技能匹配计算 (权重40%)
    SELECT COALESCE(
        (SELECT AVG(
            CASE
                WHEN ws.skill_level >= rs.skill_level_required THEN 100
                WHEN ws.skill_level = rs.skill_level_required - 1 THEN 80
                ELSE 50
            END
        )
        FROM recruitment_skill rs
        LEFT JOIN worker_skill ws ON rs.skill_id = ws.skill_id AND ws.worker_id = p_worker_id
        WHERE rs.recruitment_id = p_recruitment_id), 0
    ) INTO v_skill_score;

    -- 地点匹配计算 (权重20%)
    SELECT CASE
        WHEN w.current_location LIKE CONCAT('%', SUBSTRING_INDEX(r.work_location, ' ', 1), '%') THEN 100
        WHEN w.current_location LIKE CONCAT('%', SUBSTRING_INDEX(r.work_location, ' ', 2), '%') THEN 80
        ELSE 30
    END INTO v_location_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 薪资匹配计算 (权重15%)
    SELECT CASE
        WHEN w.expected_salary_min <= r.salary_max AND w.expected_salary_max >= r.salary_min THEN 100
        WHEN w.expected_salary_min <= r.salary_max * 1.2 THEN 80
        ELSE 40
    END INTO v_salary_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 经验匹配计算 (权重15%)
    SELECT CASE
        WHEN w.work_experience_years >=
            CASE r.experience_requirement
                WHEN '1-3年' THEN 1
                WHEN '3-5年' THEN 3
                WHEN '5年以上' THEN 5
                ELSE 0
            END THEN 100
        ELSE 60
    END INTO v_experience_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 学历匹配计算 (权重5%)
    SELECT CASE
        WHEN r.education_requirement IS NULL OR r.education_requirement = '' THEN 100
        WHEN w.education_level = r.education_requirement THEN 100
        ELSE 70
    END INTO v_education_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 时间匹配计算 (权重5%)
    SELECT CASE
        WHEN w.available_start_date <= r.work_start_date
             AND (w.available_end_date IS NULL OR w.available_end_date >= r.work_end_date) THEN 100
        WHEN w.available_start_date <= r.work_start_date + INTERVAL 7 DAY THEN 80
        ELSE 50
    END INTO v_time_score
    FROM recruitment_info r, worker_info w
    WHERE r.recruitment_id = p_recruitment_id AND w.worker_id = p_worker_id;

    -- 计算总分
    SET p_match_score = (
        v_skill_score * 0.4 +
        v_location_score * 0.2 +
        v_salary_score * 0.15 +
        v_experience_score * 0.15 +
        v_education_score * 0.05 +
        v_time_score * 0.05
    );

    -- 插入或更新匹配记录
    INSERT INTO match_record (
        recruitment_id, worker_id, match_score,
        skill_match_score, location_match_score, salary_match_score,
        experience_match_score, education_match_score, time_match_score,
        match_time, create_time
    ) VALUES (
        p_recruitment_id, p_worker_id, p_match_score,
        v_skill_score, v_location_score, v_salary_score,
        v_experience_score, v_education_score, v_time_score,
        NOW(), NOW()
    ) ON DUPLICATE KEY UPDATE
        match_score = p_match_score,
        skill_match_score = v_skill_score,
        location_match_score = v_location_score,
        salary_match_score = v_salary_score,
        experience_match_score = v_experience_score,
        education_match_score = v_education_score,
        time_match_score = v_time_score,
        match_time = NOW(),
        update_time = NOW();

END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
